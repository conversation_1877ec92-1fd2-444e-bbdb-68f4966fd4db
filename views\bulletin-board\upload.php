<?php
/**
 * Secure file upload handler for CKEditor
 * Implements comprehensive security validation
 */

function secureFileUpload($file) {
    // 1. Validate file exists and no errors
    if (!isset($file) || $file['error'] !== UPLOAD_ERR_OK) {
        throw new Exception('File upload error');
    }

    // 2. Validate file size (5MB limit)
    $maxSize = 5 * 1024 * 1024; // 5MB
    if ($file['size'] > $maxSize) {
        throw new Exception('File too large. Maximum size is 5MB.');
    }

    // 3. Validate file extension (whitelist approach)
    $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif'];
    $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if (!in_array($extension, $allowedExtensions)) {
        throw new Exception('Invalid file type. Only JPG, PNG, and GIF images are allowed.');
    }

    // 4. Validate MIME type
    $allowedMimeTypes = ['image/jpeg', 'image/png', 'image/gif'];
    $finfo = finfo_open(FILEINFO_MIME_TYPE);
    $mimeType = finfo_file($finfo, $file['tmp_name']);
    finfo_close($finfo);

    if (!in_array($mimeType, $allowedMimeTypes)) {
        throw new Exception('Invalid file format detected.');
    }

    // 5. Validate image content using getimagesize
    $imageInfo = getimagesize($file['tmp_name']);
    if ($imageInfo === false) {
        throw new Exception('File is not a valid image.');
    }

    // 6. Scan for embedded malicious code
    $content = file_get_contents($file['tmp_name']);
    $maliciousPatterns = [
        '/<\?php/i',
        '/<\?=/i',
        '/eval\s*\(/i',
        '/base64_decode\s*\(/i',
        '/system\s*\(/i',
        '/exec\s*\(/i',
        '/shell_exec\s*\(/i',
        '/passthru\s*\(/i'
    ];

    foreach ($maliciousPatterns as $pattern) {
        if (preg_match($pattern, $content)) {
            throw new Exception('Malicious content detected in file.');
        }
    }

    // 7. Generate secure filename
    $secureFilename = bin2hex(random_bytes(16)) . '.' . $extension;

    // 8. Ensure upload directory exists and is secure
    $uploadDir = 'uploaded_pics/';
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }

    // 9. Move file securely
    $targetPath = $uploadDir . $secureFilename;
    if (!move_uploaded_file($file['tmp_name'], $targetPath)) {
        throw new Exception('Failed to save uploaded file.');
    }

    return $secureFilename;
}

// Main upload handling
if (isset($_FILES['upload']['name'])) {
    try {
        $secureFilename = secureFileUpload($_FILES['upload']);

        // CKEditor callback
        $function_number = isset($_GET['CKEditorFuncNum']) ? (int)$_GET['CKEditorFuncNum'] : 0;
        $url = 'uploaded_pics/' . $secureFilename;
        $message = 'File uploaded successfully';

        echo "<script type='text/javascript'>window.parent.CKEDITOR.tools.callFunction($function_number, '$url', '$message');</script>";

    } catch (Exception $e) {
        // Handle errors securely
        $function_number = isset($_GET['CKEditorFuncNum']) ? (int)$_GET['CKEditorFuncNum'] : 0;
        $error_message = htmlspecialchars($e->getMessage(), ENT_QUOTES, 'UTF-8');

        echo "<script type='text/javascript'>window.parent.CKEDITOR.tools.callFunction($function_number, '', '$error_message');</script>";
    }
}
?>
