<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%carousel}}`.
 */
class m250121_000000_create_carousel_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%carousel}}', [
            'id' => $this->primaryKey(),
            'title' => $this->string(255)->notNull()->comment('Carousel slide title'),
            'subtitle' => $this->text()->comment('Carousel slide subtitle/description'),
            'background_image_path' => $this->string(500)->comment('Path to background image file'),
            'foreground_image_path' => $this->string(500)->comment('Path to foreground image file (e.g., people, objects)'),
            'display_order' => $this->integer()->notNull()->defaultValue(0)->comment('Display order (lower numbers first)'),
            'is_active' => $this->boolean()->notNull()->defaultValue(true)->comment('Whether slide is active/visible'),
            'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP')->comment('Creation timestamp'),
            'updated_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')->comment('Last update timestamp'),
        ]);

        // Add indexes for better performance
        $this->createIndex('idx-carousel-is_active', '{{%carousel}}', 'is_active');
        $this->createIndex('idx-carousel-display_order', '{{%carousel}}', 'display_order');
        $this->createIndex('idx-carousel-active_order', '{{%carousel}}', ['is_active', 'display_order']);

        // Insert some default carousel items to maintain existing functionality
        $this->batchInsert('{{%carousel}}', [
            'title', 'subtitle', 'background_image_path', 'foreground_image_path', 'display_order', 'is_active'
        ], [
            [
                'Welcome to St. Rose of Lima',
                'Come and Join Us in Crockett, CA',
                'images/st_rose_header1/galaxy_2.jpg',
                'images/st_rose_header1/people.png',
                1,
                1
            ],
            [
                'Apart from the cross, there is no other ladder<br>by which we may get to heaven',
                '- St. Rose of Lima',
                'images/st_rose_header1/red_curtain.jpg',
                null,
                2,
                1
            ],
            [
                'Faith, Hope, and Love',
                'Building our community together',
                'images/st_rose_header1/cross-dark.jpg',
                null,
                3,
                1
            ]
        ]);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%carousel}}');
    }
}
