<?php

namespace app\models;

use Yii;
use yii\base\Model;

/**
 * LoginForm is the model behind the login form.
 *
 * @property User|null $user This property is read-only.
 */
class LoginForm extends Model
{
    public $username;
    public $password;
    public $rememberMe = true;

    private $_user = false;

    /**
     * @return array the validation rules.
     */
    public function rules()
    {
        return [
            // username and password are both required
            [['username', 'password'], 'required'],
            // rememberMe must be a boolean value
            ['rememberMe', 'boolean'],
            // password is validated by validatePassword()
            ['password', 'validatePassword'],
            // Add brute force validation
            ['username', 'validateBruteForce'],
        ];
    }

    /**
     * Validates against brute force attacks
     * @param string $attribute
     * @param array $params
     */
    public function validateBruteForce($attribute, $params)
    {
        $ip = Yii::$app->request->userIP ?? 'unknown';
        $cacheKey = 'login_attempts_' . $ip;
        $attempts = Yii::$app->cache->get($cacheKey) ?: 0;

        // Allow max 5 attempts per 15 minutes
        if ($attempts >= 5) {
            $this->addError($attribute, 'Too many failed login attempts. Please try again in 15 minutes.');
        }
    }

    /**
     * Validates the password.
     * This method serves as the inline validation for password.
     *
     * @param string $attribute the attribute currently being validated
     * @param array $params the additional name-value pairs given in the rule
     */
    public function validatePassword($attribute, $params)
    {
        if (!$this->hasErrors()) {
            $user = $this->getUser();

            if (!$user || !$user->validatePassword($this->password)) {
                // Record failed attempt
                $this->recordFailedAttempt();
                $this->addError($attribute, 'Incorrect username or password.');
            }
        }
    }

    /**
     * Records a failed login attempt for brute force protection
     */
    private function recordFailedAttempt()
    {
        $ip = Yii::$app->request->userIP ?? 'unknown';
        $cacheKey = 'login_attempts_' . $ip;
        $attempts = Yii::$app->cache->get($cacheKey) ?: 0;
        $attempts++;

        // Store for 15 minutes
        Yii::$app->cache->set($cacheKey, $attempts, 900);

        // Log security event
        Yii::warning("Failed login attempt #{$attempts} for user '{$this->username}' from IP: {$ip}", __CLASS__);
    }

    /**
     * Clears failed login attempts on successful login
     */
    private function clearFailedAttempts()
    {
        $ip = Yii::$app->request->userIP ?? 'unknown';
        $cacheKey = 'login_attempts_' . $ip;
        Yii::$app->cache->delete($cacheKey);
    }

    /**
     * Logs in a user using the provided username and password.
     * @return bool whether the user is logged in successfully
     */
    public function login()
    {
        if ($this->validate()) {
            $success = Yii::$app->user->login($this->getUser(), $this->rememberMe ? 3600*24*30 : 0);

            if ($success) {
                // Clear failed attempts on successful login
                $this->clearFailedAttempts();

                // Log successful login
                $ip = Yii::$app->request->userIP ?? 'unknown';
                Yii::info("Successful login for user '{$this->username}' from IP: {$ip}", __CLASS__);
            }

            return $success;
        }
        return false;
    }

    /**
     * Finds user by [[username]]
     *
     * @return User|null
     */
    public function getUser()
    {
        if ($this->_user === false) {
            $this->_user = User::findByUsername($this->username);
        }

        return $this->_user;
    }
}
