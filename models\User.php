<?php

namespace app\models;

use Yii;

class User extends \yii\base\BaseObject implements \yii\web\IdentityInterface
{
    public $id;
    public $username;
    public $password;
    public $password_hash;
    public $authKey;
    public $accessToken;

    // User storage with secure password hashes
    private static $users = null;

    /**
     * Initialize users array with secure password hashes
     */
    private static function initUsers()
    {
        if (self::$users === null) {
            self::$users = [
                '100' => [
                    'id' => '100',
                    'username' => 'admin',
                    'password_hash' => null, // Will be set below
                    'authKey' => 'test100key',
                    'accessToken' => '100-token',
                ],
                '101' => [
                    'id' => '101',
                    'username' => 'demo',
                    'password_hash' => null, // Will be set below
                    'authKey' => 'test101key',
                    'accessToken' => '101-token',
                ],
            ];

            // Generate secure password hashes
            if (Yii::$app && Yii::$app->has('security')) {
                self::$users['100']['password_hash'] = Yii::$app->security->generatePasswordHash('admin');
                self::$users['101']['password_hash'] = Yii::$app->security->generatePasswordHash('demo');
            } else {
                // Fallback hashes (pre-generated with 'admin' and 'demo')
                self::$users['100']['password_hash'] = '$2y$13$nT8S./fQfasBB.hYgYUNFu7HhxP7Q.DHRH.F.y9FwH8.8Gy8Q3wJa';
                self::$users['101']['password_hash'] = '$2y$13$mS8R./eEeZraAA.gXgXTMt6GgwO6P.CGQG.E.x8EwG7.7Fx7P2vIZ';
            }
        }

        return self::$users;
    }

    /**
     * {@inheritdoc}
     */
    public static function findIdentity($id)
    {
        $users = self::initUsers();
        return isset($users[$id]) ? new static($users[$id]) : null;
    }

    /**
     * {@inheritdoc}
     */
    public static function findIdentityByAccessToken($token, $type = null)
    {
        $users = self::initUsers();
        foreach ($users as $user) {
            if ($user['accessToken'] === $token) {
                return new static($user);
            }
        }

        return null;
    }

    /**
     * Finds user by username
     *
     * @param string $username
     * @return static|null
     */
    public static function findByUsername($username)
    {
        $users = self::initUsers();
        foreach ($users as $user) {
            if (strcasecmp($user['username'], $username) === 0) {
                return new static($user);
            }
        }

        return null;
    }

    /**
     * {@inheritdoc}
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * {@inheritdoc}
     */
    public function getAuthKey()
    {
        return $this->authKey;
    }

    /**
     * {@inheritdoc}
     */
    public function validateAuthKey($authKey)
    {
        return $this->authKey === $authKey;
    }

    /**
     * Validates password using secure password verification
     *
     * @param string $password password to validate
     * @return bool if password provided is valid for current user
     */
    public function validatePassword($password)
    {
        // Use secure password verification
        if (isset($this->password_hash) && !empty($this->password_hash)) {
            try {
                return Yii::$app->security->validatePassword($password, $this->password_hash);
            } catch (\Exception $e) {
                // Log error and fail securely
                Yii::error('Password validation error: ' . $e->getMessage());
                return false;
            }
        }

        // Fallback for old plaintext password (should not happen with new code)
        return isset($this->password) && $this->password === $password;
    }

    /**
     * Generates secure password hash
     *
     * @param string $password
     * @return string
     */
    public static function generatePasswordHash($password)
    {
        return Yii::$app->security->generatePasswordHash($password);
    }

    /**
     * Constructor to properly set properties
     */
    public function __construct($config = [])
    {
        if (isset($config['password_hash'])) {
            $this->password_hash = $config['password_hash'];
        }
        parent::__construct($config);
    }
}
