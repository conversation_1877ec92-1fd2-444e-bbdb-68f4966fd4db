<?php

namespace app\controllers;

use Yii;
use app\models\BulletinBoard;
use app\models\search\BulletinBoard as BulletinBoardSearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\helpers\ArrayHelper;

/**
 * BulletinBoardController implements the CRUD actions for BulletinBoard model.
 */
class BulletinBoardController extends Controller
{
    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            'access' => [
                'class' => \yii\filters\AccessControl::className(),
                'only' => ['index', 'view','create', 'update', 'delete','update-album',
                    'Ckeditor_image_upload', 'copy-insert'],
                'rules' => [
                    [
                        'actions' => ['index', 'view','create', 'update', 'delete', 'update-album',
                            'Ckeditor_image_upload', 'copy-insert'],
                        'allow' => true,
                        'roles' => ['@'],
                    ],
                ],
            ],

            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all BulletinBoard models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new BulletinBoardSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
        $view_date = ArrayHelper::map(BulletinBoard::find()->orderBy('view_date')->asArray()->all(), 'view_date', 'view_date');

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
            'view_date' => $view_date
        ]);
    }

    public function actionTest()
    {
        return $this->render('test');
    }

    /**
     * Secure file upload handler for CKEditor
     */
    private function secureFileUpload($file) {
        // 1. Validate file exists and no errors
        if (!isset($file) || $file['error'] !== UPLOAD_ERR_OK) {
            throw new \Exception('File upload error');
        }

        // 2. Validate file size (5MB limit)
        $maxSize = 5 * 1024 * 1024; // 5MB
        if ($file['size'] > $maxSize) {
            throw new \Exception('File too large. Maximum size is 5MB.');
        }

        // 3. Validate file extension (whitelist approach)
        $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif'];
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($extension, $allowedExtensions)) {
            throw new \Exception('Invalid file type. Only JPG, PNG, and GIF images are allowed.');
        }

        // 4. Validate MIME type
        $allowedMimeTypes = ['image/jpeg', 'image/png', 'image/gif'];
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);

        if (!in_array($mimeType, $allowedMimeTypes)) {
            throw new \Exception('Invalid file format detected.');
        }

        // 5. Validate image content using getimagesize
        $imageInfo = getimagesize($file['tmp_name']);
        if ($imageInfo === false) {
            throw new \Exception('File is not a valid image.');
        }

        // 6. Scan for embedded malicious code
        $content = file_get_contents($file['tmp_name']);
        $maliciousPatterns = [
            '/<\?php/i',
            '/<\?=/i',
            '/eval\s*\(/i',
            '/base64_decode\s*\(/i',
            '/system\s*\(/i',
            '/exec\s*\(/i',
            '/shell_exec\s*\(/i',
            '/passthru\s*\(/i'
        ];

        foreach ($maliciousPatterns as $pattern) {
            if (preg_match($pattern, $content)) {
                throw new \Exception('Malicious content detected in file.');
            }
        }

        // 7. Generate secure filename
        $secureFilename = bin2hex(random_bytes(16)) . '.' . $extension;

        // 8. Ensure upload directory exists and is secure
        $uploadDir = Yii::getAlias('@webroot') . '/uploaded_pics/';
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }

        // 9. Move file securely
        $targetPath = $uploadDir . $secureFilename;
        if (!move_uploaded_file($file['tmp_name'], $targetPath)) {
            throw new \Exception('Failed to save uploaded file.');
        }

        return $secureFilename;
    }

    public function actionCkeditor_image_upload()
    {
        $funcNum = isset($_REQUEST['CKEditorFuncNum']) ? (int)$_REQUEST['CKEditorFuncNum'] : 0;
        $message = '';
        $url = '';

        if (isset($_FILES['upload'])) {
            try {
                $secureFilename = $this->secureFileUpload($_FILES['upload']);

                // Create URL for the uploaded file
                $url = Yii::$app->urlManager->createAbsoluteUrl('/uploaded_pics/' . $secureFilename);
                $message = 'File uploaded successfully';

            } catch (\Exception $e) {
                $message = $e->getMessage();
                $url = '';
            }
        } else {
            $message = 'No file uploaded';
        }

        // Return CKEditor callback
        echo '<script type="text/javascript">window.parent.CKEDITOR.tools.callFunction("'
            .$funcNum.'", "'.htmlspecialchars($url, ENT_QUOTES, 'UTF-8').'", "'.htmlspecialchars($message, ENT_QUOTES, 'UTF-8').'" );</script>';
    }

    /**
     * Displays a single BulletinBoard model.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new BulletinBoard model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new BulletinBoard();

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            //return $this->redirect(['view', 'id' => $model->id]);
            return $this->redirect(['index']);
        }

        return $this->render('create', [
            'model' => $model,
        ]);
    }

    public function actionCopyInsert($id)
    {
        $oldmodel = $this->findModel($id);
        $model = new BulletinBoard();
        $model->message = $oldmodel->message;
        $model->view_date = "";

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            //return $this->redirect(['view', 'id' => $model->id]);
            return $this->redirect(['index']);
        }

        return $this->render('copy-insert', [
            'model' => $model,
            'clone' => 1
        ]);
    }

    /**
     * Updates an existing BulletinBoard model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            //return $this->redirect(['view', 'id' => $model->id]);
            return $this->redirect(['index']);
        }

        return $this->render('update', [
            'model' => $model,
        ]);
    }

    /**
     * Deletes an existing BulletinBoard model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the BulletinBoard model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return BulletinBoard the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = BulletinBoard::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException(Yii::t('app', 'The requested page does not exist.'));
    }
}
