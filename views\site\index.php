<?php

/* @var $this yii\web\View */

use yii\helpers\Url;
use yii\helpers\Html;
use webcreator\revslider\Widget as Slider;
use app\models\Event;
//use cinghie\fontawesome\FontAwesomeAsset;
use yii\data\ActiveDataProvider;
use yii\widgets\ListView;
use yii\widgets\Pjax;
//use lesha724\youtubewidget;
use yii\bootstrap\Modal;
use app\models\Sermon;
use app\models\Newsletter;
use app\models\CurrentPriest;
use kartik\date\DatePicker;
use newerton\fancybox3\FancyBox;
use kartik\dialog\Dialog;
use yii\web\JsExpression;

$this->title = Yii::$app->name;

$this->registerCssFile(Yii::$app->request->baseUrl . "/views/revolution/fonts/pe-icon-7-stroke/css/pe-icon-7-stroke.css");
$this->registerCssFile(Yii::$app->request->baseUrl . "/views/revolution/fonts/font-awesome/css/font-awesome.css");

// REVOLUTION STYLE SHEETS
$this->registerCssFile(Yii::$app->request->baseUrl . "/views/revolution/css/settings.css");

// REVOLUTION LAYERS STYLES
$this->registerCssFile(Yii::$app->request->baseUrl . "/views/revolution/css/layers.css");

$style = <<<CSS
        .tp-rs-menulink {text-decoration: none}
        /* .navbar {margin-bottom: 0px;} */
CSS;
$this->registerCss($style);


// REVOLUTION NAVIGATION STYLES -->
$this->registerCssFile(Yii::$app->request->baseUrl . "/views/revolution/css/navigation.css");

// <!-- FONT AND STYLE FOR BASIC DOCUMENTS, NO NEED FOR FURTHER USAGE IN YOUR PROJECTS-->
// <!-- <link href="http://fonts.googleapis.com/css?family=Roboto%3A700%2C300" rel="stylesheet" property="stylesheet" type="text/css" media="all" /> -->
// <!-- <link rel="stylesheet" type="text/css" href="/views/assets/css/noneed.css"> -->

// REVOLUTION JS FILES -->
$this->registerJsFile(Yii::$app->request->baseUrl . "/views/revolution/js/jquery.themepunch.tools.min.js");
$this->registerJsFile(Yii::$app->request->baseUrl . "/views/revolution/js/jquery.themepunch.revolution.min.js");

// SLIDER REVOLUTION 5.0 EXTENSIONS  (Load Extensions only on Local File Systems !  The following part can be removed on Server for On Demand Loading) -->
$this->registerJsFile(Yii::$app->request->baseUrl . "/views/revolution/js/extensions/revolution.extension.actions.min.js", ['position' => \yii\web\View::POS_END]);
$this->registerJsFile(Yii::$app->request->baseUrl . "/views/revolution/js/extensions/revolution.extension.carousel.min.js", ['position' => \yii\web\View::POS_END]);
$this->registerJsFile(Yii::$app->request->baseUrl . "/views/revolution/js/extensions/revolution.extension.kenburn.min.js", ['position' => \yii\web\View::POS_END]);
$this->registerJsFile(Yii::$app->request->baseUrl . "/views/revolution/js/extensions/revolution.extension.layeranimation.min.js", ['position' => \yii\web\View::POS_END]);
$this->registerJsFile(Yii::$app->request->baseUrl . "/views/revolution/js/extensions/revolution.extension.migration.min.js", ['position' => \yii\web\View::POS_END]);
$this->registerJsFile(Yii::$app->request->baseUrl . "/views/revolution/js/extensions/revolution.extension.navigation.min.js", ['position' => \yii\web\View::POS_END]);
$this->registerJsFile(Yii::$app->request->baseUrl . "/views/revolution/js/extensions/revolution.extension.parallax.min.js", ['position' => \yii\web\View::POS_END]);
$this->registerJsFile(Yii::$app->request->baseUrl . "/views/revolution/js/extensions/revolution.extension.slideanims.min.js", ['position' => \yii\web\View::POS_END]);
$this->registerJsFile(Yii::$app->request->baseUrl . "/views/revolution/js/extensions/revolution.extension.video.min.js", ['position' => \yii\web\View::POS_END]);

echo Dialog::widget(
    [
        'libName' => 'alertDialog',
        'options' => [
            'size' => Dialog::SIZE_LARGE, // large dialog text
            'type' => Dialog::TYPE_WARNING, // bootstrap contextual color
            'title' => 'Event Information',
            'closable' => true,

        ]
    ]
);

echo Dialog::widget(
    [
        'libName' => 'eventDialog', // a custom lib name
        'options' => [
            'size' => Dialog::SIZE_LARGE, // large dialog text
            //'type' => Dialog::TYPE_SUCCESS, // bootstrap contextual color
            'type' => Dialog::TYPE_INFO,
            'title' => 'Event Information',
            'closable' => false,
            'buttons' => [
                [
                    'id' => 'close-btn',
                    'label' => "Cancel",
                    'cssClass' => 'btn-warning btn-lg',
                    'icon' => Dialog::ICON_CANCEL,
                    'action' => new JsExpression("function(dialog) {
                    dialog.close();
                    window.location.reload(true);
                }")
                ],
                [
                    'id' => 'close-btn',
                    'label' => "<i class='fas fa-check'></i> OK",
                    'cssClass' => 'btn-success',
                    'icon' => '',
                    'action' => new JsExpression("function(dialog) {
                    dialog.close();
                    window.location.reload(true);
                }")
                ],
            ]
        ]
    ]
);



?>


<?php
// Slider::begin([
//     "clientOptions" => [
//         "delay" => 10000,
//         "startwidth"  => 1170,
//         "startheight" => 500,
//         "fullWidth"   => "on",
//         "fullScreen"  => "off",
//         "hideCaptionAtLimit" => "",
//         "dottedOverlay" => "twoxtwo",
//         "navigationStyle" => "hades", // hades, hesperiden
//         "tmp" => '<div class="tp-arr-allwrapper"><div class="tp-arr-imgholder"></div></div>',
//         "fullScreenOffsetContainer" => "on",
//         //"hideTimerBar" => "on"
//         //"keyboardNavigation" => "on"
//     ],
//     "items" => [
//         [
//             "id" => 3,
//             "title" => "Slide title",
//             "bgImg" => "@web/images/st_rose_header1/galaxy_2.jpg",
//             "bgImgTitle" => "Alt title for background image",
//             "masterspeed" => 2000,
//             "enabled" => 1,
//             "transition" => "fade",  // fade, slidehorizontal,
//             "slotamount" => 5,
//             "slides" => [
//                 [
//                     "slideNumber" => 1,
//                     "options" => [
//                         "img" => "@web/images/st_rose_header1/priest2.png", //st_rose_group.png
//                         "imgTitle" => "alt image title"
//                     ],
//                     "data" => [
//                         "x" => 500,
//                         "y" => 20,
//                         "speed" => 800,
//                         "start" => 1100,
//                         "easing" => "Back.easeInOut",
//                         "endspeed" => 300
//                     ],
//                     "enabled" => 1
//                 ],
//                 [
//                     "slideNumber" => 2,
//                     "options" => [
//                         "title" => '"<i>Welcome</i> To <b>ST. ROSE of LIMA in Crockett, CA.</b>,',
//                         "subtitle" => 'Come and Join Us!"'
//                     ],
//                     "data" => [
//                         "hoffset" => 0,
//                         "x" => 100,
//                         "y" => 54,
//                         "speed" => 800,
//                         "start" => 1200,
//                         "easing" => "Back.easeInOut",
//                         "endspeed" => 300
//                     ],
//                     "enabled" => 1
//                 ],
//             ]
//         ],
//         [
//             "id" => 2,
//             "title" => "Slide title",
//             "bgImg" => "@web/images/st_rose_header1/cross-dark.jpg",
//             "bgImgTitle" => "Alt title for background image",
//             "masterspeed" => 2000,
//             "enabled" => 1,
//             "transition" => "fade",
//             "slotamount" => 5,
//             "slides" => [
//                 [
//                     "slideNumber" => 1,
//                     "options" => [
//                         "img" => "@web/images/st_rose_header1/pope_francis2.png",
//                         "imgTitle" => "St. Rose Priest"
//                     ],
//                     "data" => [
//                         "hoffset" => 0,
//                         "x" => 500,
//                         "y" => 125,
//                         "speed" => 800,
//                         "start" => 1100,
//                         "easing" => "Back.easeInOut", //Back.easeInOut
//                         "endeasing" => 300,
//                         "endspeed" => 300
//                     ],
//                     "enabled" => 1
//                 ],
//                 [
//                     "slideNumber" => 2,
//                     "options" => [
//                         "title" => '<b>“Apart from the cross, there is no other ladder by which we may get to heaven.”</b>',
//                         "subtitle" => "- St. Rose of Lima",
//                     ],
//                     "data" => [
//                         "hoffset" => 0,
//                         "x" => 20,
//                         "y" => 45,
//                         "speed" => 800,
//                         "start" => 1200,
//                         "easing" => "Back.easeInOut",
//                         "endspeed" => 300
//                     ],
//                     "enabled" => 1
//                 ],
//             ]
//         ],
//     ]
// ]);
// Slider::end();



?>

<style>
/* Enhanced carousel background and foreground handling */
.rev_slider .rev-slidebg {
    background-size: cover !important;
    background-position: center center !important;
    background-repeat: no-repeat !important;
}

/* Override Revolution Slider's default background fitting */
.rev_slider .rev-slidebg[data-bgfit="contain"] {
    background-size: contain !important;
}

.rev_slider .rev-slidebg[data-bgfit="fill"] {
    background-size: 100% 100% !important;
}

.rev_slider .rev-slidebg[data-bgfit="scale-down"] {
    background-size: contain !important;
    max-width: 100%;
    max-height: 100%;
}

/* LOADING FLASH FIX - Hide carousel until Revolution Slider initializes */
.rev_slider_wrapper {
    position: relative;
    overflow: hidden;
    opacity: 1; /* TEMPORARILY VISIBLE FOR DEBUGGING */
    transition: opacity 0.5s ease-in-out;
    min-height: 400px; /* Prevent layout shift during loading */
    background-color: #f8f9fa; /* Light background during loading */
}

.rev_slider_wrapper.tp-revslider-initialised {
    opacity: 1;
}

/* Hide all slides initially to prevent flash */
.rev_slider ul li {
    opacity: 0 !important;
    visibility: hidden !important;
}

.rev_slider.tp-revslider-initialised ul li {
    opacity: 1 !important;
    visibility: visible !important;
}

/* Loading indicator */
.rev_slider_wrapper::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 40px;
    height: 40px;
    margin: -20px 0 0 -20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 1000;
}

.rev_slider_wrapper.tp-revslider-initialised::before {
    display: none;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Better responsive handling for foreground images */
.tp-caption img {
    max-width: 100%;
    height: auto;
    object-fit: contain;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Ensure foreground images are properly positioned and visible */
.tp-caption[id*="foreground"] {
    z-index: 15 !important;
    display: block !important;
    visibility: visible !important;
}

.tp-caption[id*="foreground"] img {
    position: relative !important;
    z-index: 16 !important;
}

/* Ensure text is readable over backgrounds */
.FullSiteBlock-Title {
    text-shadow: 2px 2px 4px rgba(0,0,0,0.7) !important;
    z-index: 20 !important;
}


</style>

<!-- the ID here will be used in the JavaScript below to initialize the slider -->
<div id="menu_wrapper" class="rev_slider_wrapper fullwidthbanner-container" data-alias="dark-fullsite-block-menu169"
    data-source="gallery"
    style="margin:0px auto;background-color:transparent;padding:0px;margin-top:0px;margin-bottom:0px;">
    <div id="rev_slider_1" class="rev_slider" data-version="5.4.5" style="display:none">
        <ul>
            <?php if (!empty($carouselSlides)): ?>
                <?php foreach ($carouselSlides as $index => $slide): ?>
                    <li data-transition="fade">
                        <!-- Dynamic slide background -->
                        <img src="<?php echo Yii::$app->request->baseUrl ?>/images/st_rose_header1/dummy.png" alt=""
                            data-lazyload="<?php echo Yii::$app->request->baseUrl ?>/<?php echo Html::encode($slide->background_image_path) ?>"
                            data-bgposition="center center"
                            data-bgfit="<?php echo Html::encode($slide->background_fit ?? 'cover') ?>"
                            data-bgrepeat="no-repeat"
                            data-kenburns="on"
                            data-duration="30000"
                            data-ease="Power1.easeOut"
                            data-scalestart="100"
                            data-scaleend="<?php echo ($slide->background_fit === 'cover') ? '110' : '100' ?>"
                            data-rotatestart="0"
                            data-rotateend="0"
                            data-blurstart="0"
                            data-blurend="0"
                            data-offsetstart="0 0"
                            data-offsetend="0 0"
                            data-bgparallax="off"
                            class="rev-slidebg"
                            data-no-retina>

                        <!-- Dynamic foreground image (if exists) -->
                        <?php if ($slide->foreground_image_path): ?>
                            <div class="tp-caption tp-resizeme rs-parallaxlevel-10"
                                id="slide-<?php echo $index + 1 ?>-layer-foreground"
                                data-x="['right','right','center','center']"
                                data-hoffset="['0','0','0','0']"
                                data-y="['bottom','bottom','bottom','bottom']"
                                data-voffset="['0','0','0','0']"
                                data-width="none"
                                data-height="none"
                                data-whitespace="nowrap"
                                data-type="image"
                                data-responsive_offset="on"
                                data-frames='[{"from":"x:200px;opacity:0;","speed":1500,"to":"o:1;","delay":500,"ease":"Back.easeInOut"}, {"delay":"wait","speed":300,"to":"opacity:0;","ease":"Back.easeInOut"}]'
                                data-textAlign="['right','right','center','center']"
                                data-paddingtop="[0,0,0,0]"
                                data-paddingright="[0,0,0,0]"
                                data-paddingbottom="[0,0,0,0]"
                                data-paddingleft="[0,0,0,0]"
                                style="z-index: 10; border-width:0px;">
                                <img src="<?php echo Yii::$app->request->baseUrl ?>/<?php echo Html::encode($slide->foreground_image_path) ?>"
                                     alt=""
                                     data-ww="['300px','250px','200px','150px']"
                                     data-hh="['auto','auto','auto','auto']"
                                     data-no-retina>
                            </div>
                        <?php endif; ?>

                        <!-- Dynamic slide title with line break support -->
                        <h1 class="tp-caption FullSiteBlock-Title tp-resizeme rs-parallaxlevel-1"
                            id="slide-<?php echo $index + 1 ?>-layer-title"
                            data-x="['left','left','left','left']" data-hoffset="['50','0','10','20']"
                            data-y="['top','top','top','top']" data-voffset="['20','200','210','150']"
                            data-fontsize="['45','50','30','20']" data-lineheight="['65','60','50','40']"
                            data-width="['1500','1500','350','200']" data-height="none" data-whitespace="normal"
                            data-type="text" data-responsive_offset="on"
                            data-frames='[{"from":"y:150px;opacity:0;","speed":1500,"to":"o:1;","delay":250,"ease":"Back.easeInOut"}, {"delay":"wait","speed":300,"to":"opacity:0;","ease":"Back.easeInOut"}]'
                            data-textAlign="['left','left','left','left']" data-paddingtop="[0,0,0,0]"
                            data-paddingright="[0,0,0,0]" data-paddingbottom="[0,0,0,0]" data-paddingleft="[0,0,0,0]"
                            style="z-index: 11; min-width: 3000px; max-width: 3000px; white-space: normal; color: rgba(255, 255, 255, 1.00);">
                            <?php echo $slide->getTitleWithLineBreaks() ?>
                            <?php if ($slide->subtitle): ?>
                                <br /><?php echo $slide->getSubtitleWithLineBreaks() ?>
                            <?php endif; ?>
                        </h1>
                    </li>
                <?php endforeach; ?>
            <?php else: ?>
                <!-- Fallback to hardcoded slides when no database slides exist -->
                <li data-transition="fade">
                <!-- <img src="<?php //echo Yii::$app->request->baseUrl ?>/images/st_rose_header1/galaxy_2.jpg" alt="galaxy" class="rev-slidebg">  -->
                <img src="<?php echo Yii::$app->request->baseUrl ?>/images/st_rose_header1/dummy.png" alt=""
                    data-lazyload="<?php echo Yii::$app->request->baseUrl ?>/images/st_rose_header1/galaxy_2.jpg"
                    data-bgposition="center center" data-kenburns="on" data-duration="30000" data-ease="Power1.easeOut"
                    data-scalestart="100" data-scaleend="150" data-rotatestart="2" data-rotateend="-2"
                    data-blurstart="0" data-blurend="0" data-offsetstart="0 0" data-offsetend="0 0"
                    data-bgparallax="off" class="rev-slidebg" data-no-retina>
                <h1 class="tp-caption FullSiteBlock-Title   tp-resizeme rs-parallaxlevel-1" id="slide-3158-layer-3"
                    data-x="['left','left','left','left']" data-hoffset="['50','0','10','20']"
                    data-y="['top','top','top','top']" data-voffset="['20','200','210','150']"
                    data-fontsize="['45','50','30','20']" data-lineheight="['65','60','50','40']"
                    data-width="['1500','1500','350','200']" data-height="none" data-whitespace="normal"
                    data-type="text" data-responsive_offset="on"
                    data-frames='[{"from":"y:150px;opacity:0;","speed":1500,"to":"o:1;","delay":250,"ease":"Back.easeInOut"}, {"delay":"wait","speed":300,"to":"opacity:0;","ease":"Back.easeInOut"}]'
                    data-textAlign="['left','left','left','left']" data-paddingtop="[0,0,0,0]"
                    data-paddingright="[0,0,0,0]" data-paddingbottom="[0,0,0,0]" data-paddingleft="[0,0,0,0]"
                    style="z-index: 11; min-width: 3000px; max-width: 3000px; white-space: normal; color: rgba(255, 255, 255, 1.00);">
                    Welcome to ST. ROSE of LIMA <br />
                    in Crockett, CA., Come and join us !</h1>

                <!-- LAYER NR. 2 -->
                <div class="tp-caption   tp-resizeme rs-parallaxlevel-10" id="slide-3158-layer-11"
                    data-x="['left','left','left','left']" data-hoffset="['400','734','50','344']"
                    data-y="['top','top','top','top']" data-voffset="['20','171','174','144']" data-width="none"
                    data-height="none" data-whitespace="nowrap" data-type="image" data-responsive_offset="on"
                    data-frames='[{"from":"y:150px;opacity:0;","speed":2500,"to":"o:1;","delay":350,"ease":"Back.easeInOut"}, {"delay":"wait","speed":300,"to":"opacity:0;","ease":"Back.easeInOut"}]'
                    data-textAlign="['left','left','left','left']" data-paddingtop="[0,0,0,0]"
                    data-paddingright="[0,0,0,0]" data-paddingbottom="[0,0,0,0]" data-paddingleft="[0,0,0,0]"
                    style="z-index: 8;border-width:0px;"><img
                        src="<?php echo Yii::$app->request->baseUrl ?>/images/st_rose_header1/priest2.png" alt=""
                        data-ww="['380px','240px','180px','140px']" data-hh="['430px','280px','210px','160px']"
                        width="728" height="982" data-no-retina> </div>
            </li>
            <li data-transition="fade">
                <!-- <img src="<?php //echo Yii::$app->request->baseUrl ?>/images/st_rose_header1/cross-dark.jpg" alt="Cross" class="rev-slidebg"> -->

                <!-- BACKGROUND -->
                <!-- <img src="<?php echo Yii::$app->request->baseUrl ?>/images/st_rose_header1/dummy.png" alt=""
                    data-lazyload="<?php echo Yii::$app->request->baseUrl ?>/images/st_rose_header1/cross-dark.jpg"
                    data-bgposition="center center" data-kenburns="on" data-duration="30000" data-ease="Power1.easeOut"
                    data-scalestart="100" data-scaleend="150" data-rotatestart="2" data-rotateend="-2"
                    data-blurstart="0" data-blurend="0" data-offsetstart="0 0" data-offsetend="0 0"
                    data-bgparallax="off" class="rev-slidebg" data-no-retina> -->

                <img src="<?php echo Yii::$app->request->baseUrl ?>/images/st_rose_header1/red_curtain.jpg" alt=""
                    data-lazyload="<?php echo Yii::$app->request->baseUrl ?>/images/st_rose_header1/red_curtain.jpg"
                    data-bgposition="center center" data-kenburns="on" data-duration="30000" data-ease="Power1.easeOut"
                    data-scalestart="100" data-scaleend="150" data-rotatestart="2" data-rotateend="-2"
                    data-blurstart="0" data-blurend="0" data-offsetstart="0 0" data-offsetend="0 0"
                    data-bgparallax="off" class="rev-slidebg" data-no-retina>
                <!-- LAYER NR. 1 -->
                <h1 class="tp-caption FullSiteBlock-Title tp-resizeme rs-parallaxlevel-1" id="slide-3158-layer-3"
                    data-x="['left','left','left','left']" data-hoffset="['50','20','10','50']"
                    data-y="['top','top','top','top']" data-voffset="['20','200','210','100']"
                    data-fontsize="['45','50','30','20']" data-lineheight="['65','60','50','40']"
                    data-width="['800','700','400','300']" data-height="none" data-whitespace="normal" data-type="text"
                    data-responsive_offset="on"
                    data-frames='[{"from":"y:150px;opacity:0;","speed":1500,"to":"o:1;","delay":250,"ease":"Back.easeInOut"}, {"delay":"wait","speed":300,"to":"opacity:0;","ease":"Back.easeInOut"}]'
                    data-textAlign="['left','left','left','left']" data-paddingtop="[10,10,5,5]"
                    data-paddingright="[0,0,0,0]" data-paddingbottom="[10,10,5,5]" data-paddingleft="[0,0,0,0]"
                    style="z-index: 11; white-space: normal; color: rgba(255, 255, 255, 1.00);">
                    Be beacons of hope. Reflect God's love.<br />
                    Spread acceptance and understanding<br />
                    - Pope Leo XIV
                </h1>

                <!-- LAYER NR. 2 -->
                <!-- SUBJECT PHOTO -->
                <!-- <div class="tp-caption   tp-resizeme rs-parallaxlevel-10" id="slide-3158-layer-11"
                    data-x="['left','left','left','left']" data-hoffset="['550','734','50','344']"
                    data-y="['top','top','top','top']" data-voffset="['150','171','174','144']" data-width="none"
                    data-height="none" data-whitespace="nowrap" data-type="image" data-responsive_offset="on"
                    data-frames='[{"from":"y:150px;opacity:0;","speed":2500,"to":"o:1;","delay":350,"ease":"Back.easeInOut"}, {"delay":"wait","speed":300,"to":"opacity:0;","ease":"Back.easeInOut"}]'
                    data-textAlign="['left','left','left','left']" data-paddingtop="[0,0,0,0]"
                    data-paddingright="[0,0,0,0]" data-paddingbottom="[0,0,0,0]" data-paddingleft="[0,0,0,0]"
                    style="z-index: 8;border-width:0px;"><img
                        src="<?php echo Yii::$app->request->baseUrl ?>/images/st_rose_header1/popeleo.png" alt=""
                        data-ww="['560px','304px','243px','182px']" data-hh="['370px','409px','327px','245px']"
                        width="1024" height="1024" data-no-retina> </div> -->
                <div class="tp-caption tp-resizeme rs-parallaxlevel-10" id="slide-3158-layer-11"
                    data-x="['left','left','left','left']" data-hoffset="['550','734','50','344']"
                    data-y="['top','top','top','top']" data-voffset="['60','80','100','80']" data-width="none"
                    data-height="none" data-whitespace="nowrap" data-type="image" data-responsive_offset="on"
                    data-frames='[{"from":"y:150px;opacity:0;","speed":2500,"to":"o:1;","delay":350,"ease":"Back.easeInOut"}, {"delay":"wait","speed":300,"to":"opacity:0;","ease":"Back.easeInOut"}]'
                    data-textAlign="['left','left','left','left']" data-paddingtop="[0,0,0,0]"
                    data-paddingright="[0,0,0,0]" data-paddingbottom="[0,0,0,0]" data-paddingleft="[0,0,0,0]"
                    style="z-index: 8; border-width:0px;">

                    <img src="<?php echo Yii::$app->request->baseUrl ?>/images/st_rose_header1/popeleo.png" alt=""
                        data-ww="['300px','220px','180px','140px']" data-hh="['300px','220px','180px','140px']"
                        data-no-retina>
                </div>

            </li>
            <li data-transition="fade">
                <!-- <img src="<?php //echo Yii::$app->request->baseUrl ?>/images/st_rose_header1/galaxy_4.jpg" alt="Cross" class="rev-slidebg"> -->
                <img src="<?php echo Yii::$app->request->baseUrl ?>/images/st_rose_header1/dummy.png" alt=""
                    data-lazyload="<?php echo Yii::$app->request->baseUrl ?>/images/st_rose_header1/galaxy_4.jpg"
                    data-bgposition="center center" data-kenburns="on" data-duration="30000" data-ease="Power1.easeOut"
                    data-scalestart="100" data-scaleend="150" data-rotatestart="2" data-rotateend="-2"
                    data-blurstart="0" data-blurend="0" data-offsetstart="0 0" data-offsetend="0 0"
                    data-bgparallax="off" class="rev-slidebg" data-no-retina>
                <!-- LAYER NR. 1 -->
                <h1 class="tp-caption FullSiteBlock-Title   tp-resizeme rs-parallaxlevel-1" id="slide-3158-layer-3"
                    data-x="['left','left','left','left']" data-hoffset="['350','0','10','0']"
                    data-y="['top','top','top','top']" data-voffset="['10','100','110','0']"
                    data-fontsize="['45','50','30','10']" data-lineheight="['65','60','50','40']"
                    data-width="['1500','1500','350','150']" data-height="none" data-whitespace="normal"
                    data-type="text" data-responsive_offset="on"
                    data-frames='[{"from":"y:150px;opacity:0;","speed":1500,"to":"o:1;","delay":250,"ease":"Back.easeInOut"}, {"delay":"wait","speed":300,"to":"opacity:0;","ease":"Back.easeInOut"}]'
                    data-textAlign="['left','left','left','left']" data-paddingtop="[0,0,0,0]"
                    data-paddingright="[0,0,0,0]" data-paddingbottom="[0,0,0,0]" data-paddingleft="[0,0,0,0]"
                    style="z-index: 11; min-width: 3000px; max-width: 3000px; white-space: normal; color: rgba(255, 255, 255, 1.00);">
                    "When we serve the poor and<br />
                    the sick, we serve Jesus. We <br />
                    must not fail to help our <br />
                    neighbors, because in them,<br />
                    we serve Jesus."<br />
                    - St. Rose

                </h1>

                <!-- LAYER NR. 2 -->
                <div class="tp-caption   tp-resizeme rs-parallaxlevel-10" id="slide-3158-layer-11"
                    data-x="['left','left','left','left']" data-hoffset="['10','734','50','344']"
                    data-y="['top','top','top','top']" data-voffset="['0','171','174','144']" data-width="none"
                    data-height="none" data-whitespace="nowrap" data-type="image" data-responsive_offset="on"
                    data-frames='[{"from":"y:150px;opacity:0;","speed":2500,"to":"o:1;","delay":350,"ease":"Back.easeInOut"}, {"delay":"wait","speed":300,"to":"opacity:0;","ease":"Back.easeInOut"}]'
                    data-textAlign="['left','left','left','left']" data-paddingtop="[0,0,0,0]"
                    data-paddingright="[0,0,0,0]" data-paddingbottom="[0,0,0,0]" data-paddingleft="[0,0,0,0]"
                    style="z-index: 8;border-width:0px;"><img
                        src="<?php echo Yii::$app->request->baseUrl ?>/images/st_rose_header1/st_rose_header.png" alt=""
                        data-ww="['250px','200px','160px','120px']" data-hh="['380px','300px','240px','180px']"
                        width="400" height="600" data-no-retina> </div>
            </li>
            <li data-transition="fade">
                <!-- <img src="<?php //echo Yii::$app->request->baseUrl ?>/images/st_rose_header1/full_3.jpg" alt="Cross" class="rev-slidebg"> -->
                <img src="<?php echo Yii::$app->request->baseUrl ?>/images/st_rose_header1/dummy.png" alt=""
                    data-lazyload="<?php echo Yii::$app->request->baseUrl ?>/images/st_rose_header1/full_3.jpg"
                    data-bgposition="center center" data-kenburns="on" data-duration="30000" data-ease="Power1.easeOut"
                    data-scalestart="100" data-scaleend="150" data-rotatestart="2" data-rotateend="-2"
                    data-blurstart="0" data-blurend="0" data-offsetstart="0 0" data-offsetend="0 0"
                    data-bgparallax="off" class="rev-slidebg" data-no-retina>
                <!-- LAYER NR. 1 -->
                <h1 class="tp-caption FullSiteBlock-Title   tp-resizeme rs-parallaxlevel-1" id="slide-3158-layer-3"
                    data-x="['left','left','left','left']" data-hoffset="['400','0','10','20']"
                    data-y="['top','top','top','top']" data-voffset="['80','200','210','150']"
                    data-fontsize="['45','50','30','20']" data-lineheight="['65','60','50','40']"
                    data-width="['1500','1500','350','200']" data-height="none" data-whitespace="normal"
                    data-type="text" data-responsive_offset="on"
                    data-frames='[{"from":"y:150px;opacity:0;","speed":1500,"to":"o:1;","delay":250,"ease":"Back.easeInOut"}, {"delay":"wait","speed":300,"to":"opacity:0;","ease":"Back.easeInOut"}]'
                    data-textAlign="['left','left','left','left']" data-paddingtop="[0,0,0,0]"
                    data-paddingright="[0,0,0,0]" data-paddingbottom="[0,0,0,0]" data-paddingleft="[0,0,0,0]"
                    style="z-index: 11; min-width: 3000px; max-width: 3000px; white-space: normal; color: rgba(255, 255, 255, 1.00);">
                    "If I have any worth, <br />it is to live my life for God."<br />
                    - St. Patrick
                </h1>

                <!-- LAYER NR. 2 -->
                <div class="tp-caption   tp-resizeme rs-parallaxlevel-10" id="slide-3158-layer-11"
                    data-x="['left','left','left','left']" data-hoffset="['20','734','50','344']"
                    data-y="['top','top','top','top']" data-voffset="['0','171','174','144']" data-width="none"
                    data-height="none" data-whitespace="nowrap" data-type="image" data-responsive_offset="on"
                    data-frames='[{"from":"y:150px;opacity:0;","speed":2500,"to":"o:1;","delay":350,"ease":"Back.easeInOut"}, {"delay":"wait","speed":300,"to":"opacity:0;","ease":"Back.easeInOut"}]'
                    data-textAlign="['left','left','left','left']" data-paddingtop="[0,0,0,0]"
                    data-paddingright="[0,0,0,0]" data-paddingbottom="[0,0,0,0]" data-paddingleft="[0,0,0,0]"
                    style="z-index: 8;border-width:0px;"><img
                        src="<?php echo Yii::$app->request->baseUrl ?>/images/st_rose_header1/st_patrick_header.png"
                        alt="" data-ww="['300px','240px','180px','140px']" data-hh="['780px','300px','240px','180px']"
                        width="700" height="1000" data-no-retina> </div>
            </li>
            <?php endif; ?>

        </ul><!-- END SLIDES LIST -->
    </div><!-- END SLIDER CONTAINER -->
</div> <!--END SLIDER CONTAINER wrapper -->

<div class='row border-margin top-space'> <!--  -->
    <div class='container'>
        <div class='col-lg-12'>
            <div class='col-lg-6 bottom-space'>
                <div class='img-container'>
                    <a title="Click to view Gallery" target='_blank'
                        href="<?php echo Url::to(['/media-photo/display-photo-web', 'id' => 'STROSE']) ?>">
                        <img class='church-image img-rounded item'
                            src='<?php echo Yii::$app->request->baseUrl ?>/images/st_rose_lima_church.jpg'>
                    </a>

                </div>
                <a href="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d1570.8473297789913!2d-122.22349042803451!3d38.054200462883465!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x80857178fd9cdcc5%3A0x8d30855638a17b42!2sSt.+Rose+of+Lima+Church!5e0!3m2!1sen!2sph!4v1537017578145"
                    class='fancybox-churchmap map-icon' title="St. Rose Map Location">
                    <div class="pull-right icon-padding-map">
                        <span class="fa-stack fa-lg ">
                            <i class="fa fa-circle fa-stack-2x icon-background1"></i>
                            <i class="fa fa-map-marker-alt fa-stack-1x icon-background2"></i>
                        </span>
                    </div>
                </a>
                <p>
                <h4><b>St. Rose of Lima Catholic Church, Crockett</b></h4>
                </p>

                <div class="sr_horiz_line2"></div>
                <p>
                <h5>555 3rd Ave. </h5>
                </p>
                <p>
                <h5>Crockett, CA 94525 </h5>
                </p>
            </div>
            <div class='col-lg-6 col-xs-12 line-space'>
                <div class='img-container'>
                    <a title="Click to view Gallery" target='_blank'
                        href="<?php echo Url::to(['/media-photo/display-photo-web', 'id' => 'STPATRICK']) ?>">
                        <img class='church-image img-rounded item'
                            src='<?php echo Yii::$app->request->baseUrl ?>/images/st_patrick_port_costa_2.jpg'>
                    </a>
                </div>
                <a href="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d12568.034454357216!2d-122.19706820660765!3d38.046885642183454!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x808571db3ab8e009%3A0xe806f5c7e3cd67a5!2sSt.+Patrick&#39;s+Mission+Catholic+Church!5e0!3m2!1sen!2sus!4v1542422072660"
                    class='fancybox-churchmap map-icon' title="St. Patrick Map Location">
                    <div class="pull-right icon-padding-map">
                        <span class="fa-stack fa-lg ">
                            <i class="fa fa-circle fa-stack-2x icon-background1"></i>
                            <i class="fa fa-map-marker-alt fa-stack-1x icon-background2"></i>
                        </span>
                    </div>
                </a>

                <p>
                <h4><b>St. Patrick Mission Catholic Church, Port Costa</b></h4>
                </p>
                <div class="sr_horiz_line2"></div>
                <p>
                <h5>287 Prospect Ave.</h5>
                </p>
                <p>
                <h5>Port Costa, CA 94569 </h5>
                </p>
            </div>
        </div>
    </div>
</div>

<?php
$currentPriest = CurrentPriest::find()->orderBy(['sequence_no' => SORT_DESC])->limit(1)->all();
if (count($currentPriest) > 0) {
    $priestName = $currentPriest[0]['priest_name'];
    $priestMsg = $currentPriest[0]['welcome_message'];
    $priestPic = $currentPriest[0]['priest_picture'];
} else {
    $priestName = "";
    $priestMsg = "";
    $priestPic = "";
}

$bulletin_title = $bulletin_board['title'];
$bulletin_msg = $bulletin_board['message'];

$news1 = $news_section1['news1'];
$news2 = $news_section2['news2'];

$initEventDate = $lstEvent;

?>

<script>
    function display_event(event_date) {
        url = '<?php echo Url::to(['/event/display-events-images', 'id' => '']) ?>' + event_date;
        window.location = url;

        // $.ajax({
        //     url: '<?php //echo Url::to(['/event/ajax-event-list']) ?>',
        //     type: 'post',
        //     // dataType: 'json',
        //     //async: false,
        //     data: 'event_date='+ event_date,
        //     success: function (data) {
        //         var obj = JSON.parse(data);
        //         table = "<div class='table-responsive'><table class='table table-striped'>";
        //         for(i=0; i < obj.length; i++){
        //             temp=obj[i];
        //             amOrPm = "";
        //             new_date = new Date(temp.date_start);
        //             hour = new_date.getHours();
        //             minute = new_date.getMinutes();
        //             if(hour < 12){
        //                 amOrPm = " am";
        //             }else{
        //                 hour = (hour > 12) ? hour-12 : hour;
        //                 amOrPm = " pm";
        //             }

        //             end_date = new Date(temp.date_end);
        //             end_hour = end_date.getHours();
        //             end_minute = end_date.getMinutes();
        //             if(end_hour < 12){
        //                 end_amOrPm = " am";
        //             }else{
        //                 end_hour = (end_hour > 12) ? end_hour-12 : end_hour;
        //                 end_amOrPm = " pm";
        //             }

        //             album = "";
        //             // if(temp.album_id.length > 0){

        //             // }

        //             // amOrPm = (new Date(temp.date_start).getHours() < 12 ) ? "am" : "pm";
        //             table += '<tr class="event_font"><td>Event: ' + temp.title  + '</td>';
        //             table += '<td>Date: ' + temp.event_date + ' <span class="label label-primary">'
        //                 + ("0" + hour).slice(-2) + ":" + ("0" + minute).slice(-2) + amOrPm
        //                 + ' - ' + ("0" + end_hour).slice(-2) + ":" + ("0" + end_minute).slice(-2) + end_amOrPm + '</span></td>';
        //             table += '';
        //             // table += '<br />End: ' + temp.date_end + '<br /><hr /></td></tr>';
        //         }
        //         table += '</table></div>';
        //         eventDialog.dialog(
        //             $('#lstpdf').html(),
        //             function(result) {

        //             }
        //         );


        //     }
        //});


        // alert('date is press');
        // return;
    }

    function initEventDate() {
        $('#event_date-kvdate').kvDatepicker('update', <?php echo $jsEventList; ?>);
    }

</script>

<div class="row announcement">
    <div class="container">
        <div class="col-lg-3 col-xs-12 col-sm-3">
            <img class="img-responsive img-rounded"
                src="<?php echo Yii::$app->request->baseUrl . "/images/priest/" . $priestPic ?>" width="100%">
            <div style="margin-bottom: 20px;" align="center"
                class='priest_name label label-warning col-lg-12 col-xs-12'><?= $priestName ?></div>
            <br /><br />
            <div
                style="background: #fff; border-radius: 25px; border: 2px solid #73AD21; padding: 20px; margin-top: 50px;">
                <div align="center" class='priest_name label label-success col-lg-12 col-xs-12'>View Events</div>
                <input type="text" hidden="hidden" id="hidden_init_value" value="<?php echo $initEventDate; ?>">
                <input type="text" hidden="hidden" id="hidden_js_init_value" value="<?php echo $jsEventList; ?>">

                <?php
                echo DatePicker::widget([
                    'name' => 'event_date',
                    'id' => 'event_date',
                    'readonly' => true,
                    'value' => $initEventDate,
                    'type' => DatePicker::TYPE_INLINE,

                    'pluginOptions' => [
                        'format' => 'yyyy-mm-dd',
                        'multidate' => true,
                        //'todayHighlight' => true,
                        'todayBtn' => true,
                        'updateViewDate' => false,
                        'gotoCurrent' => true,
                        // 'toggleActive' => false,

                    ],
                    'options' => [
                        // you can hide the input by setting the following
                        'style' => 'display:none; width:100%',

                    ],
                    'pluginEvents' => [
                        "changeDate" => "function(e) {
                                var user_rights = $('#user_rights').val();
                                if( user_rights == 'user' ){
                                    selectedDate = e.date.getFullYear()
                                    + '-' + (e.date.getMonth() + 1).toLocaleString('en-US', {minimumIntegerDigits: 2, useGrouping:false})
                                    + '-' + e.date.getDate().toLocaleString('en-US', {minimumIntegerDigits: 2, useGrouping:false});

                                    init_length = $('#hidden_init_value').val().length;
                                    current_length = $('#event_date').val().length;

                                    if( init_length > current_length ){
                                        // mark date is selected

                                        arrInitValue = $('#hidden_init_value').val().split(',');
                                        strEventDate = $('#event_date').val().split(',');

                                        var i;
                                        for (i = 0; i < arrInitValue.length; i++) {
                                            if(strEventDate.indexOf(arrInitValue[i]) == -1){
                                                selectedDate = arrInitValue[i];
                                                break;
                                            }
                                        }


                                        display_event(selectedDate);

                                    }else{
                                        alertDialog.alert(
                                            '<h4>No event yet for this date. Please select marked date.</h4>',
                                            function (result) {
                                                initEventDate();
                                            }
                                        );
                                    }
                                }else{
                                    selectedDate = e.date.getFullYear()
                                    + '-' + (e.date.getMonth() + 1).toLocaleString('en-US', {minimumIntegerDigits: 2, useGrouping:false})
                                    + '-' + e.date.getDate().toLocaleString('en-US', {minimumIntegerDigits: 2, useGrouping:false});

                                    init_length = $('#hidden_init_value').val().length;
                                    current_length = $('#event_date').val().length;

                                    if( init_length > current_length ){
                                        // mark date is selected

                                        arrInitValue = $('#hidden_init_value').val().split(',');
                                        strEventDate = $('#event_date').val().split(',');

                                        var i;
                                        for (i = 0; i < arrInitValue.length; i++) {
                                            if(strEventDate.indexOf(arrInitValue[i]) == -1){
                                                selectedDate = arrInitValue[i];
                                                break;
                                            }
                                        }

                                        temp = $('#rubenlink').attr('href') + selectedDate;
                                        $('#rubenlink').attr('href',temp)
                                        c = document.getElementById('rubenlink');
                                        c.click()
                                        return;

                                    }else{
                                        // eventDialog.alert(
                                        //     '<h4>No event yet for this date. Please select marked date.</h4>',
                                        //     function (result) {
                                        //         //window.location.reload(true);
                                        //         initEventDate();
                                        //     }
                                        // );
                                        alertDialog.alert(
                                            '<h4>No event yet for this date. Please select marked date.</h4>',
                                            function (result) {
                                                initEventDate();
                                            }
                                        );
                                    }
                                }
                            }"


                    ]
                ]);
                ?>
            </div>
            <br />
        </div>
        <div class="col-lg-9 col-xs-12 col-sm-8"
            style='overflow-y: auto; height:600px; padding-left: 25px; padding-right: 25px;'>
            <div class="row">
                <?= $bulletin_msg ?>
            </div>
        </div>
    </div>
</div>

<div class="row news_section">
    <div class="container">
        <div class="col-xs-12 col-sm-6 col-lg-6" style='overflow-y: auto; height:600px;'>
            <div class="row">
                <div style='padding: 20px;'>
                    <?= $news1 ?>
                </div>
            </div>
        </div>
        <div class="col-xs-12 col-sm-6 col-lg-6" style='overflow-y: auto; height:600px;'>
            <div class="row">
                <div style='padding: 20px;'>
                    <?= $news2 ?>
                </div>
            </div>
        </div>
    </div>
</div>

<a style="display: none;" data-fancybox data-type="iframe" id="ajax_content"
    data-src="<?php echo Yii::$app->request->baseUrl . '/images/event/'; ?>">data</a>

<div id="ajax_title" style="display: none;">

</div>

<!-- for ajax load of image/pdf file to upload -->
<div style="display: none;">
    <input type='text' id="user_rights" value="<?= Yii::$app->user->isGuest ? 'user' : 'admin' ?>">
    <input type='text' id="event_value">

    <!-- display calendar with time on the side -->
    <?= Html::a('<b>Load Events</b>', ['event/ajax-load-events', 'id' => ''], ['id' => 'rubenlink']); ?>
</div>




<!-- Slider's main "init" script -->
<script type="text/javascript">

    /* https://learn.jquery.com/using-jquery-core/document-ready/ */
    jQuery(document).ready(function () {

        // DEBUG: Check if all dependencies are available
        console.log('=== REVOLUTION SLIDER DEBUG START ===');
        console.log('jQuery available:', typeof jQuery !== 'undefined');
        console.log('jQuery version:', jQuery.fn.jquery);
        console.log('Revolution Slider plugin available:', typeof jQuery.fn.revolution !== 'undefined');
        console.log('Slider element exists:', jQuery('#rev_slider_1').length > 0);
        console.log('Wrapper element exists:', jQuery('.rev_slider_wrapper').length > 0);

        // Check if Revolution Slider CSS is loaded
        var hasRevSliderCSS = false;
        for (var i = 0; i < document.styleSheets.length; i++) {
            try {
                var sheet = document.styleSheets[i];
                if (sheet.href && (sheet.href.indexOf('revolution') !== -1 || sheet.href.indexOf('revslider') !== -1)) {
                    hasRevSliderCSS = true;
                    console.log('Revolution Slider CSS found:', sheet.href);
                    break;
                }
            } catch (e) {
                // Cross-origin stylesheets may throw errors
            }
        }
        console.log('Revolution Slider CSS loaded:', hasRevSliderCSS);

        /* initialize the slider based on the Slider's ID attribute from the wrapper above */
        var revSlider;
        try {
            console.log('Attempting to initialize Revolution Slider...');
            revSlider = jQuery('#rev_slider_1').show().revolution({

            /* options are 'auto', 'fullwidth' or 'fullscreen' */
            sliderLayout: 'fullwidth',

            /* basic navigation arrows and bullets */
            navigation: {

                arrows: {
                    enable: true,
                    style: 'uranus',
                    hide_onleave: true

                },

                bullets: {
                    enable: true,
                    style: 'hermes',

                    hide_onleave: false,
                    h_align: "center",
                    v_align: "bottom",
                    h_offset: 0,
                    v_offset: 20,
                    space: 5

                }
            }
        });

        console.log('Revolution Slider initialization completed successfully');

        } catch (error) {
            console.error('Revolution Slider initialization failed:', error);
            console.error('Error details:', error.message, error.stack);

            // If Revolution Slider fails to initialize, show the wrapper anyway
            jQuery('.rev_slider_wrapper').addClass('tp-revslider-initialised');
            jQuery('#rev_slider_1').show();

            // Try to show any content that might be there
            jQuery('.rev_slider_wrapper').css({
                'opacity': '1',
                'background-color': '#f0f0f0'
            });

            return; // Exit early if initialization failed
        }

        // DEBUG: Add extensive logging to track initialization
        console.log('Revolution Slider object created:', revSlider);
        console.log('Slider wrapper element:', jQuery('.rev_slider_wrapper'));
        console.log('Slider element:', jQuery('#rev_slider_1'));

        // Check if Revolution Slider actually initialized
        if (typeof revSlider.revredraw === 'function') {
            console.log('Revolution Slider methods are available');
        } else {
            console.error('Revolution Slider methods not available - initialization failed');
        }

        // TEMPORARY: Remove opacity hiding to see if content is there
        jQuery('.rev_slider_wrapper').css('opacity', '1');
        console.log('Temporarily showing slider wrapper');

        // Use Revolution Slider's built-in callback approach
        if (revSlider && typeof revSlider.bind === 'function') {
            revSlider.bind('revolution.slide.onloaded', function() {
                console.log('Revolution Slider onloaded event fired');
                jQuery('.rev_slider_wrapper').addClass('tp-revslider-initialised');
            });

            revSlider.bind('revolution.slide.onchange', function() {
                console.log('Revolution Slider onchange event fired');
                jQuery('.rev_slider_wrapper').addClass('tp-revslider-initialised');
            });

            revSlider.bind('revolution.slide.onafterswap', function() {
                console.log('Revolution Slider onafterswap event fired');
                jQuery('.rev_slider_wrapper').addClass('tp-revslider-initialised');
            });
        } else {
            console.error('Revolution Slider bind method not available');
        }

        // Polling mechanism to check when slider is ready
        var checkSliderReady = function() {
            var sliderElement = jQuery('#rev_slider_1');
            var wrapperElement = jQuery('.rev_slider_wrapper');

            console.log('Checking slider readiness...');
            console.log('Slider display:', sliderElement.css('display'));
            console.log('Slider visibility:', sliderElement.css('visibility'));
            console.log('Wrapper has tp-revslider-initialised:', wrapperElement.hasClass('tp-revslider-initialised'));

            // Check if slider has been initialized by Revolution Slider
            if (sliderElement.hasClass('tp-revslider-initialised') ||
                sliderElement.find('.tp-revslider-mainul').length > 0 ||
                sliderElement.css('display') !== 'none') {

                console.log('Slider appears to be ready, showing wrapper');
                wrapperElement.addClass('tp-revslider-initialised');
                return true;
            }
            return false;
        };

        // Start polling
        var pollCount = 0;
        var pollInterval = setInterval(function() {
            pollCount++;
            console.log('Poll attempt:', pollCount);

            if (checkSliderReady() || pollCount >= 20) {
                clearInterval(pollInterval);
                if (pollCount >= 20) {
                    console.warn('Polling timeout reached, forcing slider visibility');
                    jQuery('.rev_slider_wrapper').addClass('tp-revslider-initialised');
                }
            }
        }, 250);

        // Final fallback
        setTimeout(function() {
            console.log('Final fallback timeout reached');
            jQuery('.rev_slider_wrapper').addClass('tp-revslider-initialised');
        }, 3000);


        $().fancybox({
            maxWidth: 800,
            maxHeight: 600,
            fitToView: false,
            width: '70%',
            height: '70%',
            autoSize: false,
            closeClick: false,
            openEffect: 'fade',
            closeEffect: 'fade',
            smallBtn: true,
            showCloseButton: true,
        });

        function doClickEvent() {
            $('#event_link').trigger('click');
        }





        // $('#event_date-kvdate').kvDatepicker( {
        //     onSelect: function(date) {
        //         alert('On select');
        //     },

        // });




        // $('#event_date-kvdate').kvDatepicker("update", <?php //= $initEventDate ?>);
        //$('#event_date-kvdate').kvDatepicker("viewDate", '2019-05-23');

        // $('#event_date-kvdate').data('DateTimePicker').('gotoCurrent');

        // $('#event_date-kvdate').data("DateTimePicker").viewDate('2019-05-23');

        //$("#event_date-kvdate").kvDatepicker("setDates", ["08/01/2016","09/01/2016","10/01/2016","11/30/2016","05/24/2019", "05/24/2019"]);

        // $("#event_date-kvdate").kvDatepicker({
        //     'gotoCurrent': true
        // });

        // $("#event_date-kvdate").kvDatepicker("defaultViewDate", new Date()); //ok
        // $("#event_date-kvdate").kvDatepicker("toggleActive"); //ok
        // $("#event_date-kvdate").kvDatepicker("toggleActive"); //ok
    });

</script>
