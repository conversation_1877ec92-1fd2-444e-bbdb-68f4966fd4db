<?php

use yii\helpers\Html;
use yii\grid\GridView;
use yii\widgets\Pjax;

/* @var $this yii\web\View */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Carousel Management';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="carousel-index">

    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title">
                        <i class="fa fa-images"></i> <?= Html::encode($this->title) ?>
                    </h3>
                </div>
                <div class="panel-body">
                    
                    <div class="row">
                        <div class="col-md-12">
                            <p class="help-block">
                                <i class="fa fa-info-circle"></i> 
                                Manage carousel slides that appear on the homepage. Use the display order to control the sequence of slides.
                            </p>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <?= Html::a('<i class="fa fa-plus"></i> Create New Slide', ['create'], [
                                'class' => 'btn btn-success',
                                'style' => 'margin-bottom: 15px;'
                            ]) ?>
                        </div>
                    </div>

                    <?php Pjax::begin(); ?>
                    
                    <?= GridView::widget([
                        'dataProvider' => $dataProvider,
                        'tableOptions' => ['class' => 'table table-striped table-bordered'],
                        'columns' => [
                            [
                                'attribute' => 'display_order',
                                'label' => 'Order',
                                'headerOptions' => ['style' => 'width: 80px; text-align: center;'],
                                'contentOptions' => ['style' => 'text-align: center; vertical-align: middle;'],
                                'value' => function ($model) {
                                    return $model->display_order;
                                },
                            ],
                            [
                                'attribute' => 'background_image_path',
                                'label' => 'Image',
                                'format' => 'raw',
                                'headerOptions' => ['style' => 'width: 120px; text-align: center;'],
                                'contentOptions' => ['style' => 'text-align: center; vertical-align: middle;'],
                                'value' => function ($model) {
                                    if ($model->background_image_path) {
                                        $imageUrl = Yii::$app->request->baseUrl . '/' . $model->background_image_path;
                                        return Html::img($imageUrl, [
                                            'style' => 'max-width: 100px; max-height: 60px; border-radius: 4px;',
                                            'class' => 'img-thumbnail'
                                        ]);
                                    }
                                    return '<span class="text-muted"><i class="fa fa-image"></i> No image</span>';
                                },
                            ],
                            [
                                'attribute' => 'title',
                                'label' => 'Title',
                                'format' => 'raw',
                                'value' => function ($model) {
                                    $title = Html::encode($model->title);
                                    if ($model->subtitle) {
                                        $subtitle = Html::encode($model->subtitle);
                                        return "<strong>{$title}</strong><br><small class='text-muted'>{$subtitle}</small>";
                                    }
                                    return "<strong>{$title}</strong>";
                                },
                            ],
                            [
                                'attribute' => 'is_active',
                                'label' => 'Status',
                                'format' => 'raw',
                                'headerOptions' => ['style' => 'width: 100px; text-align: center;'],
                                'contentOptions' => ['style' => 'text-align: center; vertical-align: middle;'],
                                'value' => function ($model) {
                                    if ($model->is_active) {
                                        return '<span class="label label-success"><i class="fa fa-check"></i> Active</span>';
                                    } else {
                                        return '<span class="label label-default"><i class="fa fa-times"></i> Inactive</span>';
                                    }
                                },
                            ],
                            [
                                'attribute' => 'created_at',
                                'label' => 'Created',
                                'format' => 'datetime',
                                'headerOptions' => ['style' => 'width: 140px;'],
                                'contentOptions' => ['style' => 'vertical-align: middle;'],
                            ],
                            [
                                'class' => 'yii\grid\ActionColumn',
                                'header' => 'Actions',
                                'headerOptions' => ['style' => 'width: 200px; text-align: center;'],
                                'contentOptions' => ['style' => 'text-align: center; vertical-align: middle;'],
                                'template' => '{move-up} {move-down} {toggle} {view} {update} {delete}',
                                'buttons' => [
                                    'move-up' => function ($url, $model, $key) {
                                        return Html::a('<i class="fa fa-arrow-up"></i>', ['move-up', 'id' => $model->id], [
                                            'class' => 'btn btn-xs btn-info',
                                            'title' => 'Move Up',
                                            'data-method' => 'post',
                                            'data-confirm' => 'Move this slide up in the display order?',
                                        ]);
                                    },
                                    'move-down' => function ($url, $model, $key) {
                                        return Html::a('<i class="fa fa-arrow-down"></i>', ['move-down', 'id' => $model->id], [
                                            'class' => 'btn btn-xs btn-info',
                                            'title' => 'Move Down',
                                            'data-method' => 'post',
                                            'data-confirm' => 'Move this slide down in the display order?',
                                        ]);
                                    },
                                    'toggle' => function ($url, $model, $key) {
                                        $class = $model->is_active ? 'btn-warning' : 'btn-success';
                                        $icon = $model->is_active ? 'fa-eye-slash' : 'fa-eye';
                                        $title = $model->is_active ? 'Deactivate' : 'Activate';
                                        $confirm = $model->is_active ? 'Deactivate this slide?' : 'Activate this slide?';
                                        
                                        return Html::a("<i class='fa {$icon}'></i>", ['toggle-status', 'id' => $model->id], [
                                            'class' => "btn btn-xs {$class}",
                                            'title' => $title,
                                            'data-method' => 'post',
                                            'data-confirm' => $confirm,
                                        ]);
                                    },
                                    'view' => function ($url, $model, $key) {
                                        return Html::a('<i class="fa fa-eye"></i>', ['view', 'id' => $model->id], [
                                            'class' => 'btn btn-xs btn-primary',
                                            'title' => 'View',
                                        ]);
                                    },
                                    'update' => function ($url, $model, $key) {
                                        return Html::a('<i class="fa fa-edit"></i>', ['update', 'id' => $model->id], [
                                            'class' => 'btn btn-xs btn-default',
                                            'title' => 'Edit',
                                        ]);
                                    },
                                    'delete' => function ($url, $model, $key) {
                                        return Html::a('<i class="fa fa-trash"></i>', ['delete', 'id' => $model->id], [
                                            'class' => 'btn btn-xs btn-danger',
                                            'title' => 'Delete',
                                            'data-method' => 'post',
                                            'data-confirm' => 'Are you sure you want to delete this carousel slide? This action cannot be undone.',
                                        ]);
                                    },
                                ],
                            ],
                        ],
                    ]); ?>
                    
                    <?php Pjax::end(); ?>

                </div>
            </div>
        </div>
    </div>

</div>

<?php
// Add some custom CSS for better styling
$this->registerCss("
.carousel-index .btn-xs {
    margin: 1px;
}
.carousel-index .img-thumbnail {
    border: 1px solid #ddd;
}
.carousel-index .help-block {
    margin-bottom: 15px;
    padding: 10px;
    background-color: #f5f5f5;
    border-left: 4px solid #5bc0de;
}
");
?>
