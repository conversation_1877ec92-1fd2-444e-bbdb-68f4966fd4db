<?php

namespace app\controllers;

use Yii;
use yii\filters\AccessControl;
use yii\web\Controller;
use yii\web\Response;
use yii\filters\VerbFilter;
use app\models\LoginForm;
use app\models\ContactForm;
use app\models\ChurchInfo;
use app\models\Quote;
use app\models\BulletinBoard;
use app\models\NewsSection1;
use app\models\NewsSection2;
use app\models\Event;
use app\models\Carousel;
use yii\db\Expression;
use yii\web\NotFoundHttpException;

class SiteController extends Controller
{
    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            'access' => [
                'class' => AccessControl::className(),
                'only' => [ 'view','create', 'update', 'delete', 'logout'],
                'rules' => [
                    [
                        'actions' => [ 'view','create', 'update', 'delete', 'logout'],
                        'allow' => true,
                        'roles' => ['@'],
                    ],
                ],
            ],
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'logout' => ['post'],
                ],
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function actions()
    {
        return [
            'error' => [
                'class' => 'yii\web\ErrorAction',
            ],
            'captcha' => [
                'class' => 'yii\captcha\CaptchaAction',
                'fixedVerifyCode' => YII_ENV_TEST ? 'testme' : null,
            ],
        ];
    }

    /**
     * Displays homepage.
     *
     * @return string
     */
    public function actionIndex()
    {
        // get list of Mission
        $mission = ChurchInfo::find()->where(['category' => 10])->orderBy(['id' => SORT_ASC])->all();
        
        //get current month
        //$temp = new \yii\db\Expression('NOW()');
        $date_quote =  date('m');
        $selected_quoted="";

        $quote = Quote::find()->where(['month_visible' => $date_quote, 'is_active' => 1])
            ->orWhere(['month_visible' => $date_quote])
            ->orderBy(new Expression('rand()'))->limit(1)->all();

        $current_date = date('Y-m-d');

        // Get active carousel slides
        $carouselSlides = Carousel::getActiveItems()->all();

        $bulletin_board = BulletinBoard::find()
            ->where(['<=', 'view_date', $current_date])
            ->orderby(['view_date' => SORT_DESC])
            ->one();
        $news_section1 = NewsSection1::find()
            ->where(['<=', 'view_date', $current_date])
            ->orderby(['view_date' => SORT_DESC])
            ->one();
        $news_section2 = NewsSection2::find()
            ->where(['<=', 'view_date', $current_date])
            ->orderby(['view_date' => SORT_DESC])
            ->one();

        // get the list of event dates for initial value of datepicker that have marks
        
        $eventDates = Event::find()
        
            // ->select(['event_date', 'event_image', 'title'] )
            // ->where(['not', ['event_image' => null]])
            // ->orWhere(['not', ['title' => null]])
            // ->orderby(['event_date' => SORT_ASC])
            // ->all();

            // $condition = "daily_mass = 0 and (daily_mass = 1 and event_date >= ".$current_date.")";
            ->select(['event_date'])
            ->andWhere(['daily_mass' => 0])
            ->orWhere(['and',
                ['daily_mass' => 1],
                ['>=', 'event_date', $current_date ]
            ])
            // ->orWhere(['daily_mass' => 1])
            // ->orWhere(['not', ['title' => null]])
            // date('Y-m-d',strtotime(date('Y-m-d') . "+1 days"))
            ->distinct()
            ->orderby(['event_date' => SORT_ASC])
            ->all();

        // var_dump( $eventDates->createCommand()->getRawSql() ); die();
  
        $lstEvent = ""; 
        $jsEventList = "";
        foreach($eventDates as $temp){
            $lstEvent = $lstEvent . $temp['event_date'].",";
            $jsEventList .= "'".$temp['event_date']."',";
        }
        if(strlen($lstEvent) > 0){
            $lstEvent = substr($lstEvent, 0, -1);
            $jsEventList = substr($jsEventList, 0, -1);
        }

        return $this->render('index',[
            'mission' => $mission,
            'quote' => $quote,
            'bulletin_board' => $bulletin_board,
            'news_section1' => $news_section1,
            'news_section2' => $news_section2,
            'lstEvent' => $lstEvent,
            'jsEventList' => $jsEventList,
            'carouselSlides' => $carouselSlides
        ]);
    }

    /**
     * Login action.
     *
     * @return Response|string
     */
    public function actionLogin()
    {
        if (!Yii::$app->user->isGuest) {
            return $this->goHome();
        }

        $model = new LoginForm();
        if ($model->load(Yii::$app->request->post()) && $model->login()) {
            return $this->goBack();
        }

        $model->password = '';
        return $this->render('login', [
            'model' => $model,
        ]);
    }

    public function actionAnalytics()
    {
        return $this->render('analytics');
    }

    /**
     * Logout action.
     *
     * @return Response
     */
    public function actionLogout()
    {
        Yii::$app->user->logout();

        return $this->goHome();
    }

    /**
     * Displays contact page.
     *
     * @return Response|string
     */
    public function actionContact()
    {
        $model = new ContactForm();
        if ($model->load(Yii::$app->request->post()) && $model->contact(Yii::$app->params['adminEmail'])) {
            Yii::$app->session->setFlash('contactFormSubmitted');

            return $this->refresh();
        }
        return $this->render('contact', [
            'model' => $model,
        ]);
    }

    public function actionSendEmail()
    {
        return $this->render('send-email', [
            //'model' => $model,
        ]);
    }

    /**
     * Displays about page.
     *
     * @return string
     */
    public function actionAbout()
    {
        $id = $_GET['id'];
        //$model = ChurchInfo::find()->where(['id'=>$id]);
        $model = ChurchInfo::findOne($id);

        if ($model  !== null) { //= ChurchInfo::findOne($id) 
            //var_dump($model); die();
            return $this->render('about', ['model' => $model]);
        }

        throw new NotFoundHttpException(Yii::t('app', 'The requested page does not exist.'));
        // $model = ChurchInfo::findOne($id);

        // return $this->render('about', ['model' => $model]);
    }
}
