<?php

namespace app\controllers;

use Yii;
use app\models\Carousel;
use yii\data\ActiveDataProvider;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\filters\AccessControl;
use yii\web\UploadedFile;
use yii\web\Response;
use app\helpers\SecureUploadHelper;

/**
 * CarouselController implements the CRUD actions for Carousel model.
 * Admin-only access for managing carousel slides.
 */
class CarouselController extends Controller
{
    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            'access' => [
                'class' => AccessControl::class,
                'only' => ['index', 'view', 'create', 'update', 'delete', 'move-up', 'move-down', 'toggle-status'],
                'rules' => [
                    [
                        'actions' => ['index', 'view', 'create', 'update', 'delete', 'move-up', 'move-down', 'toggle-status'],
                        'allow' => true,
                        'roles' => ['@'],
                        'matchCallback' => function ($rule, $action) {
                            // Check if user is admin
                            return Yii::$app->user->identity && 
                                   (in_array(Yii::$app->user->identity->username, ['strose_admin', 'rvoliquino']) ||
                                    Yii::$app->user->can('admin'));
                        }
                    ],
                ],
            ],
            'verbs' => [
                'class' => VerbFilter::class,
                'actions' => [
                    'delete' => ['POST'],
                    'move-up' => ['POST'],
                    'move-down' => ['POST'],
                    'toggle-status' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all Carousel models.
     * @return mixed
     */
    public function actionIndex()
    {
        $dataProvider = new ActiveDataProvider([
            'query' => Carousel::getAllItems(),
            'pagination' => [
                'pageSize' => 20,
            ],
            'sort' => [
                'defaultOrder' => [
                    'display_order' => SORT_ASC,
                    'id' => SORT_ASC,
                ]
            ],
        ]);

        return $this->render('index', [
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single Carousel model.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new Carousel model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new Carousel();

        if ($model->load(Yii::$app->request->post())) {
            // Handle background image upload
            $model->imageFile = UploadedFile::getInstance($model, 'imageFile');

            if ($model->imageFile) {
                try {
                    $filename = SecureUploadHelper::uploadCarouselImage($model->imageFile);
                    $model->background_image_path = 'uploads/carousel/' . $filename;
                } catch (\Exception $e) {
                    Yii::$app->session->setFlash('error', 'Background image upload failed: ' . $e->getMessage());
                    return $this->render('create', ['model' => $model]);
                }
            }

            // Handle foreground image upload
            $model->foregroundImageFile = UploadedFile::getInstance($model, 'foregroundImageFile');

            if ($model->foregroundImageFile) {
                try {
                    $filename = SecureUploadHelper::uploadCarouselImage($model->foregroundImageFile);
                    $model->foreground_image_path = 'uploads/carousel/' . $filename;
                } catch (\Exception $e) {
                    Yii::$app->session->setFlash('error', 'Foreground image upload failed: ' . $e->getMessage());
                    return $this->render('create', ['model' => $model]);
                }
            }

            if ($model->save()) {
                Yii::$app->session->setFlash('success', 'Carousel slide created successfully.');
                return $this->redirect(['view', 'id' => $model->id]);
            } else {
                Yii::$app->session->setFlash('error', 'Failed to create carousel slide.');
            }
        }

        return $this->render('create', [
            'model' => $model,
        ]);
    }

    /**
     * Updates an existing Carousel model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);
        $oldImagePath = $model->background_image_path;
        $oldForegroundPath = $model->foreground_image_path;

        if ($model->load(Yii::$app->request->post())) {
            // Handle background image upload
            $model->imageFile = UploadedFile::getInstance($model, 'imageFile');

            if ($model->imageFile) {
                try {
                    $filename = SecureUploadHelper::uploadCarouselImage($model->imageFile);
                    $model->background_image_path = 'uploads/carousel/' . $filename;

                    // Delete old image if upload was successful
                    if ($oldImagePath && $oldImagePath !== $model->background_image_path) {
                        SecureUploadHelper::deleteFileByPath($oldImagePath);
                    }
                } catch (\Exception $e) {
                    Yii::$app->session->setFlash('error', 'Background image upload failed: ' . $e->getMessage());
                    return $this->render('update', ['model' => $model]);
                }
            }

            // Handle foreground image upload
            $model->foregroundImageFile = UploadedFile::getInstance($model, 'foregroundImageFile');

            if ($model->foregroundImageFile) {
                try {
                    $filename = SecureUploadHelper::uploadCarouselImage($model->foregroundImageFile);
                    $model->foreground_image_path = 'uploads/carousel/' . $filename;

                    // Delete old foreground image if upload was successful
                    if ($oldForegroundPath && $oldForegroundPath !== $model->foreground_image_path) {
                        SecureUploadHelper::deleteFileByPath($oldForegroundPath);
                    }
                } catch (\Exception $e) {
                    Yii::$app->session->setFlash('error', 'Foreground image upload failed: ' . $e->getMessage());
                    return $this->render('update', ['model' => $model]);
                }
            }

            if ($model->save()) {
                Yii::$app->session->setFlash('success', 'Carousel slide updated successfully.');
                return $this->redirect(['view', 'id' => $model->id]);
            } else {
                Yii::$app->session->setFlash('error', 'Failed to update carousel slide.');
            }
        }

        return $this->render('update', [
            'model' => $model,
        ]);
    }

    /**
     * Deletes an existing Carousel model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionDelete($id)
    {
        $model = $this->findModel($id);
        
        if ($model->delete()) {
            Yii::$app->session->setFlash('success', 'Carousel slide deleted successfully.');
        } else {
            Yii::$app->session->setFlash('error', 'Failed to delete carousel slide.');
        }

        return $this->redirect(['index']);
    }

    /**
     * Move carousel item up in display order
     * @param integer $id
     * @return Response
     * @throws NotFoundHttpException
     */
    public function actionMoveUp($id)
    {
        $model = $this->findModel($id);
        
        if ($model->moveUp()) {
            Yii::$app->session->setFlash('success', 'Carousel slide moved up successfully.');
        } else {
            Yii::$app->session->setFlash('error', 'Cannot move slide up.');
        }

        return $this->redirect(['index']);
    }

    /**
     * Move carousel item down in display order
     * @param integer $id
     * @return Response
     * @throws NotFoundHttpException
     */
    public function actionMoveDown($id)
    {
        $model = $this->findModel($id);
        
        if ($model->moveDown()) {
            Yii::$app->session->setFlash('success', 'Carousel slide moved down successfully.');
        } else {
            Yii::$app->session->setFlash('error', 'Cannot move slide down.');
        }

        return $this->redirect(['index']);
    }

    /**
     * Toggle active status of carousel item
     * @param integer $id
     * @return Response
     * @throws NotFoundHttpException
     */
    public function actionToggleStatus($id)
    {
        $model = $this->findModel($id);
        $model->is_active = !$model->is_active;
        
        if ($model->save(false)) {
            $status = $model->is_active ? 'activated' : 'deactivated';
            Yii::$app->session->setFlash('success', "Carousel slide {$status} successfully.");
        } else {
            Yii::$app->session->setFlash('error', 'Failed to update carousel slide status.');
        }

        return $this->redirect(['index']);
    }

    /**
     * Finds the Carousel model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return Carousel the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = Carousel::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }
}
