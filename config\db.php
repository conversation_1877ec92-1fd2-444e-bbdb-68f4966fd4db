<?php

return [
    'class' => 'yii\db\Connection',
    'dsn' => 'mysql:host=localhost;dbname=stroseo1_strose_db',
    'username' => 'stroseo1_rvoliqu',

    // Secure password configuration - use environment variable if available
    'password' => getenv('DB_PASSWORD') ?: 'Hulaanmo007',

    // Security configurations
    'attributes' => [
        // Use prepared statements (default in Yii2, but explicitly set)
        PDO::ATTR_EMULATE_PREPARES => false,

        // Force persistent connections to false for security
        PDO::ATTR_PERSISTENT => false,

        // Error mode for better debugging in development
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
    ],

    // Schema cache options (for production environment)
    'enableSchemaCache' => true,
    'schemaCacheDuration' => 3600, // 1 hour
    'schemaCache' => 'cache',
    
    // Connection pool settings
    'maxPoolSize' => 20,
    'connectionTimeout' => 5, // seconds
    'serverRetryTimeout' => 5, // seconds

    // Query logging and profiling (only in debug mode for security)
    'enableLogging' => YII_DEBUG,
    'enableProfiling' => YII_DEBUG,

    // Connection timeout and charset
    'charset' => 'utf8mb4',
];
