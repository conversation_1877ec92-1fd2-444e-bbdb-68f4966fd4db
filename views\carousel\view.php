<?php

use yii\helpers\Html;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model app\models\Carousel */

$this->title = $model->title;
$this->params['breadcrumbs'][] = ['label' => 'Carousel Management', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="carousel-view">

    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title">
                        <i class="fa fa-eye"></i> Carousel Slide Details
                    </h3>
                </div>
                <div class="panel-body">
                    
                    <div class="row">
                        <div class="col-md-12">
                            <div class="btn-group" style="margin-bottom: 15px;">
                                <?= Html::a('<i class="fa fa-edit"></i> Update', ['update', 'id' => $model->id], [
                                    'class' => 'btn btn-primary'
                                ]) ?>
                                <?= Html::a('<i class="fa fa-trash"></i> Delete', ['delete', 'id' => $model->id], [
                                    'class' => 'btn btn-danger',
                                    'data' => [
                                        'confirm' => 'Are you sure you want to delete this carousel slide?',
                                        'method' => 'post',
                                    ],
                                ]) ?>
                                <?= Html::a('<i class="fa fa-list"></i> Back to List', ['index'], [
                                    'class' => 'btn btn-default'
                                ]) ?>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            <?= DetailView::widget([
                                'model' => $model,
                                'options' => ['class' => 'table table-striped table-bordered detail-view'],
                                'attributes' => [
                                    'id',
                                    'title',
                                    [
                                        'attribute' => 'subtitle',
                                        'value' => $model->subtitle ?: 'No subtitle',
                                    ],
                                    [
                                        'attribute' => 'display_order',
                                        'label' => 'Display Order',
                                    ],
                                    [
                                        'attribute' => 'is_active',
                                        'format' => 'raw',
                                        'value' => $model->is_active 
                                            ? '<span class="label label-success"><i class="fa fa-check"></i> Active</span>'
                                            : '<span class="label label-default"><i class="fa fa-times"></i> Inactive</span>',
                                    ],
                                    [
                                        'attribute' => 'background_image_path',
                                        'label' => 'Image Path',
                                        'value' => $model->background_image_path ?: 'No image',
                                    ],
                                    [
                                        'attribute' => 'created_at',
                                        'format' => 'datetime',
                                    ],
                                    [
                                        'attribute' => 'updated_at',
                                        'format' => 'datetime',
                                    ],
                                ],
                            ]) ?>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="panel panel-info">
                                <div class="panel-heading">
                                    <h4 class="panel-title">
                                        <i class="fa fa-image"></i> Background Image
                                    </h4>
                                </div>
                                <div class="panel-body text-center">
                                    <?php if ($model->background_image_path): ?>
                                        <?php $imageUrl = Yii::$app->request->baseUrl . '/' . $model->background_image_path; ?>
                                        <?= Html::img($imageUrl, [
                                            'class' => 'img-responsive img-thumbnail',
                                            'style' => 'max-width: 100%; height: auto;'
                                        ]) ?>
                                        <p class="text-muted" style="margin-top: 10px;">
                                            <small><?= Html::encode(basename($model->background_image_path)) ?></small>
                                        </p>
                                    <?php else: ?>
                                        <div class="text-muted" style="padding: 40px;">
                                            <i class="fa fa-image fa-3x"></i>
                                            <p>No image uploaded</p>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                            
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    <h4 class="panel-title">
                                        <i class="fa fa-cogs"></i> Quick Actions
                                    </h4>
                                </div>
                                <div class="panel-body">
                                    <div class="btn-group-vertical btn-block">
                                        <?= Html::a('<i class="fa fa-arrow-up"></i> Move Up', ['move-up', 'id' => $model->id], [
                                            'class' => 'btn btn-info btn-sm',
                                            'data-method' => 'post',
                                            'data-confirm' => 'Move this slide up in the display order?',
                                        ]) ?>
                                        <?= Html::a('<i class="fa fa-arrow-down"></i> Move Down', ['move-down', 'id' => $model->id], [
                                            'class' => 'btn btn-info btn-sm',
                                            'data-method' => 'post',
                                            'data-confirm' => 'Move this slide down in the display order?',
                                        ]) ?>
                                        <?php
                                        $toggleClass = $model->is_active ? 'btn-warning' : 'btn-success';
                                        $toggleIcon = $model->is_active ? 'fa-eye-slash' : 'fa-eye';
                                        $toggleText = $model->is_active ? 'Deactivate' : 'Activate';
                                        $toggleConfirm = $model->is_active ? 'Deactivate this slide?' : 'Activate this slide?';
                                        ?>
                                        <?= Html::a("<i class='fa {$toggleIcon}'></i> {$toggleText}", ['toggle-status', 'id' => $model->id], [
                                            'class' => "btn {$toggleClass} btn-sm",
                                            'data-method' => 'post',
                                            'data-confirm' => $toggleConfirm,
                                        ]) ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>

</div>
