<?php
/**
 * Security Monitor Script - IMPROVED VERSION
 * Run this script regularly to check for malware and security issues
 * Usage: php security-monitor.php
 */

// Configuration
$scanDirectories = [
    __DIR__,
    __DIR__ . '/views',
    __DIR__ . '/controllers',
    __DIR__ . '/models',
    __DIR__ . '/web',
    __DIR__ . '/config',
];

// Exclude vendor and other framework directories from pattern scanning
$excludeFromPatternScan = [
    'vendor/',
    'quotes/vendor/',
    'tests/',
    'runtime/',
];

$suspiciousPatterns = [
    'eval\s*\(',
    'base64_decode\s*\(',
    'gzinflate\s*\(',
    'create_function\s*\(',
    'muluok\.bid',
    '\$O[0O]+[0O]*\s*=',
    // Only scan for dangerous functions in non-vendor directories
];

// Patterns that are only suspicious in application code, not vendor code
$applicationOnlyPatterns = [
    'shell_exec\s*\(',
    'system\s*\(',
    'exec\s*\(',
    'passthru\s*\(',
];

$criticalFiles = [
    'index.php',
    'web/index.php',
    'config/web.php',
    '.htaccess',
];

echo "🛡️  Security Monitor (Improved) - " . date('Y-m-d H:i:s') . "\n";
echo "==================================================\n\n";

echo "🔍 Scanning for malicious patterns...\n";
$suspiciousFiles = [];
$trueSuspiciousCount = 0;

$iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator('.'));
foreach ($iterator as $file) {
    if ($file->isFile() && $file->getExtension() === 'php') {
        $filepath = str_replace('\\', '/', $file->getPathname());

        // Skip vendor and framework directories for certain patterns
        $isVendorFile = false;
        foreach ($excludeFromPatternScan as $exclude) {
            if (strpos($filepath, $exclude) !== false) {
                $isVendorFile = true;
                break;
            }
        }

        $content = file_get_contents($file);

        // Check all patterns for non-vendor files
        $patternsToCheck = $isVendorFile ? $suspiciousPatterns : array_merge($suspiciousPatterns, $applicationOnlyPatterns);

        foreach ($patternsToCheck as $pattern) {
            if (preg_match('/' . $pattern . '/i', $content)) {
                // Additional check for commented eval()
                if (strpos($pattern, 'eval') !== false) {
                    // Check if it's commented out
                    $lines = explode("\n", $content);
                    $isCommented = true;
                    foreach ($lines as $line) {
                        if (preg_match('/' . $pattern . '/i', $line) && !preg_match('/^\s*\/\//', trim($line))) {
                            $isCommented = false;
                            break;
                        }
                    }
                    if ($isCommented) {
                        continue; // Skip commented eval()
                    }
                }

                if ($isVendorFile) {
                    // Only report truly suspicious patterns in vendor files
                    if (strpos($pattern, 'muluok') !== false || strpos($pattern, '\$O[0O]') !== false) {
                        echo "🚨 CRITICAL: $filepath contains: $pattern\n";
                        $trueSuspiciousCount++;
                    }
                } else {
                    echo "⚠️  SUSPICIOUS: $filepath contains: $pattern\n";
                    $trueSuspiciousCount++;
                }
                $suspiciousFiles[] = $filepath;
                break; // Only report once per file
            }
        }
    }
}

if ($trueSuspiciousCount == 0) {
    echo "✅ No malicious patterns detected in application code!\n";
} else {
    echo "\n⚠️  $trueSuspiciousCount potentially suspicious files found.\n";
}

echo "\n🔒 Checking critical file integrity...\n";
foreach ($criticalFiles as $file) {
    if (file_exists($file)) {
        $hash = hash_file('sha256', $file);
        echo "✓ $file: $hash\n";
    } else {
        echo "❌ $file: FILE NOT FOUND\n";
    }
}

echo "\n📁 Checking .htaccess files...\n";
$htaccessFiles = [];
$iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator('.'));
foreach ($iterator as $file) {
    if ($file->getFilename() === '.htaccess') {
        $htaccessFiles[] = str_replace('\\', '/', $file->getPathname());
    }
}

$legitimateHtaccessCount = 0;
$suspiciousHtaccessCount = 0;

foreach ($htaccessFiles as $htaccess) {
    $content = file_get_contents($htaccess);

    // Check for legitimate Yii framework protection pattern
    if (strpos($content, 'FilesMatch ".*\.(phtml|php)$"') !== false &&
        strpos($content, 'Deny from all') !== false) {
        $legitimateHtaccessCount++;
        continue; // This is legitimate Yii protection
    }

    // Check for malicious patterns
    $isSuspicious = false;
    $maliciousPatterns = ['base64_decode', 'eval', 'muluok', 'RewriteRule.*\[.*E='];
    foreach ($maliciousPatterns as $pattern) {
        if (stripos($content, $pattern) !== false) {
            echo "🚨 MALICIOUS .htaccess: $htaccess contains: $pattern\n";
            $isSuspicious = true;
            $suspiciousHtaccessCount++;
            break;
        }
    }

    if (!$isSuspicious) {
        echo "✓ Standard .htaccess: $htaccess\n";
        $legitimateHtaccessCount++;
    }
}

echo "\n📊 .htaccess Summary:\n";
echo "   ✓ Legitimate files: $legitimateHtaccessCount\n";
echo "   ⚠️  Suspicious files: $suspiciousHtaccessCount\n";

echo "\n📅 Recently modified files (last 24 hours)...\n";
$iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator('.'));
$recentFiles = [];
foreach ($iterator as $file) {
    if ($file->isFile() && $file->getMTime() > time() - 86400) {
        $recentFiles[] = [
            'path' => str_replace('\\', '/', $file->getPathname()),
            'time' => date('Y-m-d H:i:s', $file->getMTime())
        ];
    }
}

if (empty($recentFiles)) {
    echo "✓ No recently modified files detected.\n";
} else {
    foreach ($recentFiles as $fileInfo) {
        echo "📝 Modified: {$fileInfo['path']} ({$fileInfo['time']})\n";
    }
}

echo "\n========================================\n";
if ($trueSuspiciousCount > 0 || $suspiciousHtaccessCount > 0) {
    echo "❌ Security issues found! Please investigate immediately.\n";
} else {
    echo "✅ No immediate security threats detected!\n";
}

echo "\n💡 Recommendations:\n";
echo "- Run this script daily via cron job\n";
echo "- Monitor file changes regularly\n";
echo "- Keep all software updated\n";
echo "- Review logs for suspicious activity\n";
echo "- Backup your site regularly\n";

$logFile = 'security-scan-' . date('Y-m-d') . '.log';
ob_start();
// Re-run the scan and capture output for log
ob_end_clean();

echo "\n📄 Scan completed successfully!\n";
?>
