<?php

namespace app\models;

use Yii;
use yii\db\ActiveRecord;
use yii\behaviors\TimestampBehavior;
use yii\web\UploadedFile;
use yii\helpers\FileHelper;

/**
 * This is the model class for table "carousel".
 *
 * @property int $id
 * @property string $title
 * @property string|null $subtitle
 * @property string|null $background_image_path
 * @property string|null $foreground_image_path
 * @property string $background_fit
 * @property int $display_order
 * @property bool $is_active
 * @property string $created_at
 * @property string $updated_at
 */
class Carousel extends ActiveRecord
{
    /**
     * @var UploadedFile
     */
    public $imageFile;

    /**
     * @var UploadedFile
     */
    public $foregroundImageFile;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'carousel';
    }

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'createdAtAttribute' => 'created_at',
                'updatedAtAttribute' => 'updated_at',
                'value' => function() {
                    return date('Y-m-d H:i:s');
                },
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['title'], 'required'],
            [['subtitle'], 'string'],
            [['display_order'], 'integer', 'min' => 0],
            [['is_active'], 'boolean'],
            [['created_at', 'updated_at'], 'safe'],
            [['title'], 'string', 'max' => 255],
            [['background_image_path', 'foreground_image_path'], 'string', 'max' => 500],
            [['background_fit'], 'string', 'default', 'value' => 'cover'],
            [['background_fit'], 'in', 'range' => ['cover', 'contain', 'fill', 'scale-down']],
            [['imageFile'], 'file', 'skipOnEmpty' => true, 'extensions' => 'png, jpg, jpeg, gif', 'maxSize' => 5 * 1024 * 1024], // 5MB max
            [['foregroundImageFile'], 'file', 'skipOnEmpty' => true, 'extensions' => 'png, jpg, jpeg, gif', 'maxSize' => 5 * 1024 * 1024], // 5MB max
            [['display_order'], 'default', 'value' => function() {
                return $this->getNextDisplayOrder();
            }],
            [['is_active'], 'default', 'value' => true],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'title' => 'Title',
            'subtitle' => 'Subtitle',
            'background_image_path' => 'Background Image',
            'foreground_image_path' => 'Foreground Image',
            'background_fit' => 'Background Fit',
            'display_order' => 'Display Order',
            'is_active' => 'Active',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'imageFile' => 'Background Image',
            'foregroundImageFile' => 'Foreground Image',
        ];
    }

    /**
     * Get active carousel items ordered by display_order
     * @return \yii\db\ActiveQuery
     */
    public static function getActiveItems()
    {
        return static::find()
            ->where(['is_active' => true])
            ->orderBy(['display_order' => SORT_ASC, 'id' => SORT_ASC]);
    }

    /**
     * Get all carousel items ordered by display_order
     * @return \yii\db\ActiveQuery
     */
    public static function getAllItems()
    {
        return static::find()
            ->orderBy(['display_order' => SORT_ASC, 'id' => SORT_ASC]);
    }

    /**
     * Get the next available display order
     * @return int
     */
    protected function getNextDisplayOrder()
    {
        $maxOrder = static::find()->max('display_order');
        return $maxOrder ? $maxOrder + 1 : 1;
    }

    /**
     * Upload and save the background image
     * @return bool
     */
    public function uploadImage()
    {
        if ($this->imageFile) {
            // Create upload directory if it doesn't exist
            $uploadPath = Yii::getAlias('@webroot/uploads/carousel/');
            if (!is_dir($uploadPath)) {
                FileHelper::createDirectory($uploadPath, 0755, true);
            }

            // Generate unique filename
            $fileName = 'carousel_' . time() . '_' . uniqid() . '.' . $this->imageFile->extension;
            $filePath = $uploadPath . $fileName;

            // Save the file
            if ($this->imageFile->saveAs($filePath)) {
                // Delete old image if exists
                if ($this->background_image_path && file_exists(Yii::getAlias('@webroot/' . $this->background_image_path))) {
                    unlink(Yii::getAlias('@webroot/' . $this->background_image_path));
                }

                // Update the model
                $this->background_image_path = 'uploads/carousel/' . $fileName;
                return true;
            }
        }
        return false;
    }

    /**
     * Upload and save the foreground image
     * @return bool
     */
    public function uploadForegroundImage()
    {
        if ($this->foregroundImageFile) {
            // Create upload directory if it doesn't exist
            $uploadPath = Yii::getAlias('@webroot/uploads/carousel/');
            if (!is_dir($uploadPath)) {
                FileHelper::createDirectory($uploadPath, 0755, true);
            }

            // Generate unique filename
            $fileName = 'carousel_fg_' . time() . '_' . uniqid() . '.' . $this->foregroundImageFile->extension;
            $filePath = $uploadPath . $fileName;

            // Save the file
            if ($this->foregroundImageFile->saveAs($filePath)) {
                // Delete old image if exists
                if ($this->foreground_image_path && file_exists(Yii::getAlias('@webroot/' . $this->foreground_image_path))) {
                    unlink(Yii::getAlias('@webroot/' . $this->foreground_image_path));
                }

                // Update the model
                $this->foreground_image_path = 'uploads/carousel/' . $fileName;
                return true;
            }
        }
        return false;
    }

    /**
     * Get the full URL to the background image
     * @return string|null
     */
    public function getImageUrl()
    {
        if ($this->background_image_path) {
            return Yii::$app->request->baseUrl . '/' . $this->background_image_path;
        }
        return null;
    }

    /**
     * Get the full URL to the foreground image
     * @return string|null
     */
    public function getForegroundImageUrl()
    {
        if ($this->foreground_image_path) {
            return Yii::$app->request->baseUrl . '/' . $this->foreground_image_path;
        }
        return null;
    }

    /**
     * Get the absolute path to the background image
     * @return string|null
     */
    public function getImagePath()
    {
        if ($this->background_image_path) {
            return Yii::getAlias('@webroot/' . $this->background_image_path);
        }
        return null;
    }

    /**
     * Get the absolute path to the foreground image
     * @return string|null
     */
    public function getForegroundImagePath()
    {
        if ($this->foreground_image_path) {
            return Yii::getAlias('@webroot/' . $this->foreground_image_path);
        }
        return null;
    }

    /**
     * Get title with line breaks converted to HTML
     * @return string
     */
    public function getTitleWithLineBreaks()
    {
        return nl2br($this->title);
    }

    /**
     * Get subtitle with line breaks converted to HTML
     * @return string|null
     */
    public function getSubtitleWithLineBreaks()
    {
        return $this->subtitle ? nl2br($this->subtitle) : null;
    }

    /**
     * Delete the associated image files when the record is deleted
     * @return bool
     */
    public function beforeDelete()
    {
        if (parent::beforeDelete()) {
            // Delete associated background image file
            if ($this->background_image_path) {
                $imagePath = $this->getImagePath();
                if ($imagePath && file_exists($imagePath)) {
                    unlink($imagePath);
                }
            }

            // Delete associated foreground image file
            if ($this->foreground_image_path) {
                $foregroundPath = $this->getForegroundImagePath();
                if ($foregroundPath && file_exists($foregroundPath)) {
                    unlink($foregroundPath);
                }
            }
            return true;
        }
        return false;
    }

    /**
     * Move item up in display order
     * @return bool
     */
    public function moveUp()
    {
        $prevItem = static::find()
            ->where(['<', 'display_order', $this->display_order])
            ->orderBy(['display_order' => SORT_DESC])
            ->one();

        if ($prevItem) {
            $tempOrder = $this->display_order;
            $this->display_order = $prevItem->display_order;
            $prevItem->display_order = $tempOrder;

            return $this->save(false) && $prevItem->save(false);
        }
        return false;
    }

    /**
     * Move item down in display order
     * @return bool
     */
    public function moveDown()
    {
        $nextItem = static::find()
            ->where(['>', 'display_order', $this->display_order])
            ->orderBy(['display_order' => SORT_ASC])
            ->one();

        if ($nextItem) {
            $tempOrder = $this->display_order;
            $this->display_order = $nextItem->display_order;
            $nextItem->display_order = $tempOrder;

            return $this->save(false) && $nextItem->save(false);
        }
        return false;
    }
}
