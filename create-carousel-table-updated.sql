-- Updated Carousel Table Creation Script
-- Includes support for both background and foreground images, plus line breaks

CREATE TABLE IF NOT EXISTS `carousel` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL COMMENT 'Carousel slide title (supports HTML line breaks)',
  `subtitle` text COMMENT 'Carousel slide subtitle/description (supports HTML line breaks)',
  `background_image_path` varchar(500) DEFAULT NULL COMMENT 'Path to background image file',
  `foreground_image_path` varchar(500) DEFAULT NULL COMMENT 'Path to foreground image file (e.g., people, objects)',
  `display_order` int(11) NOT NULL DEFAULT 0 COMMENT 'Display order (lower numbers first)',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'Whether slide is active/visible',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT 'Creation timestamp',
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Last update timestamp',
  PRIMARY KEY (`id`),
  <PERSON><PERSON>Y `idx-carousel-is_active` (`is_active`),
  KEY `idx-carousel-display_order` (`display_order`),
  KEY `idx-carousel-active_order` (`is_active`,`display_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert default carousel items with line breaks and foreground images
INSERT IGNORE INTO `carousel` (`title`, `subtitle`, `background_image_path`, `foreground_image_path`, `display_order`, `is_active`) VALUES
('Welcome to St. Rose of Lima', 'Come and Join Us in Crockett, CA', 'images/st_rose_header1/galaxy_2.jpg', 'images/st_rose_header1/people.png', 1, 1),
('Apart from the cross, there is no other ladder<br>by which we may get to heaven', '- St. Rose of Lima', 'images/st_rose_header1/red_curtain.jpg', NULL, 2, 1),
('Faith, Hope, and Love', 'Building our community together', 'images/st_rose_header1/cross-dark.jpg', NULL, 3, 1);

-- Show the created table structure
DESCRIBE carousel;
