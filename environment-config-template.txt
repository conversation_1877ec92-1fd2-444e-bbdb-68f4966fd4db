# Environment Configuration Template for Secure Deployment
# Copy this content to a .env file in your production environment
# NEVER commit the .env file to version control

# Database Configuration
DB_PASSWORD=your_secure_database_password_here

# Application Environment
# Set to 'prod' for production, 'dev' for development
YII_ENV=prod
YII_DEBUG=0

# Security Keys (generate new ones for production)
COOKIE_VALIDATION_KEY=generate_a_new_32_character_random_string

# File Upload Settings
MAX_UPLOAD_SIZE=5242880
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif

# Email Configuration (if used)
SMTP_HOST=your_smtp_host
SMTP_PORT=587
SMTP_USERNAME=your_smtp_username
SMTP_PASSWORD=your_smtp_password

# Cache Configuration
CACHE_TYPE=file
# CACHE_TYPE=redis (if using Redis)
# REDIS_HOST=localhost
# REDIS_PORT=6379

# Security Settings
ENABLE_CSRF=1
SECURE_COOKIES=1
HTTPONLY_COOKIES=1

# Rate Limiting
LOGIN_ATTEMPTS_LIMIT=5
LOGIN_LOCKOUT_DURATION=900

# Logging
LOG_LEVEL=error
# LOG_LEVEL=info (for development)

# Maintenance Mode
MAINTENANCE_MODE=0

# Instructions:
# 1. Copy this file to .env in your project root
# 2. Replace all placeholder values with your actual configuration
# 3. Ensure .env is in your .gitignore file
# 4. Set appropriate file permissions (600) on the .env file
