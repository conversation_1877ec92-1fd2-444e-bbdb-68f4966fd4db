<?php
/**
 * Debug script for carousel issues
 * Run this to systematically check all carousel components
 */

require __DIR__ . '/vendor/autoload.php';
require __DIR__ . '/vendor/yiisoft/yii2/Yii.php';

$config = require __DIR__ . '/config/web.php';
$app = new yii\web\Application($config);

echo "=== Carousel Debug Report ===\n\n";

try {
    // 1. Check database records
    echo "1. DATABASE RECORDS:\n";
    $slides = app\models\Carousel::find()->all();
    foreach ($slides as $slide) {
        echo "   ID: {$slide->id}\n";
        echo "   Title: {$slide->title}\n";
        echo "   Background: {$slide->background_image_path}\n";
        echo "   Foreground: " . ($slide->foreground_image_path ?? 'NULL') . "\n";
        echo "   Background Fit: " . ($slide->background_fit ?? 'NULL') . "\n";
        echo "   Active: " . ($slide->is_active ? 'YES' : 'NO') . "\n";
        echo "   ---\n";
    }
    
    // 2. Check image files exist
    echo "\n2. IMAGE FILE VERIFICATION:\n";
    foreach ($slides as $slide) {
        // Check background image
        if ($slide->background_image_path) {
            $bgPath = Yii::getAlias('@webroot/' . $slide->background_image_path);
            echo "   Background [{$slide->id}]: " . ($slide->background_image_path) . " - ";
            echo (file_exists($bgPath) ? "EXISTS" : "MISSING") . "\n";
        }
        
        // Check foreground image
        if ($slide->foreground_image_path) {
            $fgPath = Yii::getAlias('@webroot/' . $slide->foreground_image_path);
            echo "   Foreground [{$slide->id}]: " . ($slide->foreground_image_path) . " - ";
            echo (file_exists($fgPath) ? "EXISTS" : "MISSING") . "\n";
        }
    }
    
    // 3. Check model attributes
    echo "\n3. MODEL ATTRIBUTES:\n";
    $model = new app\models\Carousel();
    $attributes = $model->attributes();
    echo "   Available attributes: " . implode(', ', $attributes) . "\n";
    
    // 4. Test active items query
    echo "\n4. ACTIVE ITEMS QUERY:\n";
    $activeSlides = app\models\Carousel::getActiveItems()->all();
    echo "   Active slides count: " . count($activeSlides) . "\n";
    foreach ($activeSlides as $slide) {
        echo "   - [{$slide->id}] {$slide->title} (Order: {$slide->display_order})\n";
    }
    
    // 5. Generate sample HTML output
    echo "\n5. SAMPLE HTML OUTPUT:\n";
    foreach ($activeSlides as $index => $slide) {
        echo "   Slide " . ($index + 1) . ":\n";
        echo "   Background: data-bgfit=\"" . ($slide->background_fit ?? 'cover') . "\"\n";
        echo "   Foreground: " . ($slide->foreground_image_path ? "PRESENT" : "NONE") . "\n";
        echo "   Title: " . $slide->getTitleWithLineBreaks() . "\n";
        echo "   ---\n";
    }
    
    // 6. Check specific image files mentioned
    echo "\n6. SPECIFIC IMAGE FILES:\n";
    $imagesToCheck = [
        'images/st_rose_header1/priest.png',
        'images/st_rose_header1/popeleo.png', 
        'images/st_rose_header1/st_rose_header.png',
        'images/st_rose_header1/galaxy_2.jpg',
        'images/st_rose_header1/red_curtain.jpg',
        'images/st_rose_header1/cross-dark.jpg'
    ];
    
    foreach ($imagesToCheck as $imagePath) {
        $fullPath = Yii::getAlias('@webroot/' . $imagePath);
        echo "   {$imagePath}: " . (file_exists($fullPath) ? "EXISTS" : "MISSING") . "\n";
        if (file_exists($fullPath)) {
            $size = filesize($fullPath);
            echo "     Size: " . number_format($size) . " bytes\n";
        }
    }
    
    // 7. Check Revolution Slider CSS/JS
    echo "\n7. REVOLUTION SLIDER FILES:\n";
    $revFiles = [
        'plugins/revolution/css/settings.css',
        'plugins/revolution/js/jquery.themepunch.tools.min.js',
        'plugins/revolution/js/jquery.themepunch.revolution.min.js'
    ];
    
    foreach ($revFiles as $file) {
        $fullPath = Yii::getAlias('@webroot/' . $file);
        echo "   {$file}: " . (file_exists($fullPath) ? "EXISTS" : "MISSING") . "\n";
    }
    
    // 8. Test URL generation
    echo "\n8. URL GENERATION TEST:\n";
    foreach ($activeSlides as $slide) {
        if ($slide->foreground_image_path) {
            $url = Yii::$app->request->baseUrl . '/' . $slide->foreground_image_path;
            echo "   Foreground URL [{$slide->id}]: {$url}\n";
        }
    }
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== Debug Complete ===\n";
echo "Next steps:\n";
echo "1. Run: mysql -u stroseo1_rvoliqu -p stroseo1_strose_db < update-carousel-database.sql\n";
echo "2. Check website: https://www.strosecrockett.org/\n";
echo "3. Inspect browser developer tools for console errors\n";
echo "4. Check Network tab for failed image requests\n";
?>
