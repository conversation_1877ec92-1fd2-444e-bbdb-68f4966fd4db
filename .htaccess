# Security Hardening Rules
# Block access to sensitive files
<FilesMatch "(^#.*#|\.(bak|config|sql|fla|psd|ini|log|sh|inc|swp|dist)|~)$">
    Order allow,deny
    Deny from all
    Satisfy All
</FilesMatch>

# Block access to directories without index.php
Options -Indexes

# Prevent script execution in upload directories
<Directory "uploaded_pics">
    php_flag engine off
    RemoveHandler .php .phtml .php3 .php4 .php5 .php6
    RemoveType .php .phtml .php3 .php4 .php5 .php6
</Directory>

<Directory "images">
    php_flag engine off
    RemoveHandler .php .phtml .php3 .php4 .php5 .php6
    RemoveType .php .phtml .php3 .php4 .php5 .php6
</Directory>

# Block malicious file uploads in upload directories only
# Note: Main PHP files need to execute, so we only block in specific directories
<Directory "uploaded_pics">
    <FilesMatch "\.(php|php3|php4|php5|php6|phtml|pl|py|jsp|asp|sh|cgi)$">
        <RequireAll>
            Require all denied
        </RequireAll>
    </FilesMatch>
</Directory>

<Directory "images">
    <FilesMatch "\.(php|php3|php4|php5|php6|phtml|pl|py|jsp|asp|sh|cgi)$">
        <RequireAll>
            Require all denied
        </RequireAll>
    </FilesMatch>
</Directory>

# Security headers
<IfModule mod_headers.c>
    Header always set X-Frame-Options "SAMEORIGIN"
    Header always set X-Content-Type-Options "nosniff"
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Permissions-Policy "geolocation=(), microphone=(), camera=()"
</IfModule>

# Hide server information
ServerTokens Prod
<IfModule mod_headers.c>
    Header unset Server
    Header unset X-Powered-By
</IfModule>

# Yii Framework URL Rewriting (Preserve existing functionality)
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteBase /

    # Block access to hidden files and directories
    RewriteRule (^\.|/\.) - [F]

    # Block access to backup files
    RewriteRule \.(bak|backup|swp|old|tmp)$ - [F]

    # Main Yii rewrite rules
    RewriteRule ^index\.php$ - [L]
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule . /index.php [L]
</IfModule>

# Rate limiting (if mod_evasive is available)
<IfModule mod_evasive24.c>
    DOSHashTableSize    2048
    DOSPageCount        5
    DOSPageInterval     1
    DOSSiteCount        50
    DOSSiteInterval     1
    DOSBlockingPeriod   60
</IfModule>

# php -- BEGIN cPanel-generated handler, do not edit
# Set the "ea-php74" package as the default "PHP" programming language.
<IfModule mime_module>
    AddHandler application/x-httpd-ea-php74___lsphp .php .php7 .phtml
</IfModule>