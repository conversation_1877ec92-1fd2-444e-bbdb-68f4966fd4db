<?php

$params = require __DIR__ . '/params.php';
$db = require __DIR__ . '/db.php';

$config = [
    'id' => 'ST. ROSE',
    'name' => 'ST. ROSE OF LIMA CATHOLIC CHURCH',
    'basePath' => dirname(__DIR__),
    'bootstrap' => ['log'],
    'aliases' => [
        '@bower' => '@vendor/bower-asset',
        '@npm'   => '@vendor/npm-asset',
    ],
    'timeZone' => 'America/Los_Angeles',
    'components' => [
        'request' => [
            // !!! insert a secret key in the following (if it is empty) - this is required by cookie validation
            'cookieValidationKey' => '3DQ1XEsUbzeSeoQVt0lyuD-5QF8Sk6Pn',
            // Security: Enable CSRF protection
            'enableCsrfValidation' => true,
            // Security: Secure cookies
            'csrfCookie' => [
                'httpOnly' => true,
                'secure' => true, // HTTPS enabled for production
                'sameSite' => 'strict',
            ],
            // Security: Parse trusted proxies safely
            'trustedHosts' => [
                'www.stroseoflimacrockettca.org',
                'stroseoflimacrockettca.org',
                'strosecrockett.org',
                'www.strosecrockett.org'
            ],
        ],
        'session' => [
            // Security: Secure session configuration
            'cookieParams' => [
                'httpOnly' => true,
                'secure' => true, // HTTPS enabled for production
                'sameSite' => 'strict',
            ],
            'useCookies' => true,
            'name' => 'STROSE_SESSION',
        ],
        'urlManager' => [
            'class' => 'yii\web\UrlManager',
            // Hide index.php
            'showScriptName' => false,
            // Use pretty URLs
            'enablePrettyUrl' => true,
            'rules' => [
                "home" => "site/index",
                "about" => "site/about",
                //'<alias:\w+>' => 'site/<alias>',
            ],
        ],

        // "urlManager" => [
        //     'baseUrl' => $baseUrl,
        //     'enablePrettyUrl' => true,
        //     'showScriptName' => true,
        //     "rules" => [
        //         // "home" => "site/index",
        //         // "about-us" => "site/about",
        //         // "contact-us" => "site/contact",
        //     ]
        // ],
        'cache' => [
            'class' => 'yii\caching\FileCache',
        ],
        // 'user' => [
        //     'identityClass' => 'app\models\User',
        //     'enableAutoLogin' => true,
        // ],
        'errorHandler' => [
            'errorAction' => 'site/error',
        ],
        'mailer' => [
            'class' => 'yii\swiftmailer\Mailer',
            // send all mails to a file by default. You have to set
            // 'useFileTransport' to false and configure a transport
            // for the mailer to send real emails.
            'transport' => [
                'class' => 'Swift_MailTransport',
            ],
            'useFileTransport' => false,
        ],
        'log' => [
            'traceLevel' => YII_DEBUG ? 3 : 0,
            'targets' => [
                [
                    'class' => 'yii\log\FileTarget',
                    'levels' => ['error', 'warning'],
                ],
            ],
        ],
        'db' => $db,
        'fs' => [
            'class' => 'creocoder\flysystem\LocalFilesystem',
            'path' => '@webroot/files',
        ],

        // 'assetManager' => [
        //     'bundles' => [
        //         'yii\web\JqueryAsset' => [
        //             'jsOptions' => [ 'position' => \yii\web\View::POS_HEAD ],
        //         ],
        //     ],
        // ],

    //     'view' => [
    //         'theme' => [
    //             'pathMap' => [
    //                '@app/views' => '@vendor/dmstr/yii2-adminlte-asset/example-views/yiisoft/yii2-app'
    //             ],
    //         ],
    //    ],

        /*
        'urlManager' => [
            'enablePrettyUrl' => true,
            'showScriptName' => false,
            'rules' => [
            ],
        ],
        */
    ],
    'modules' => [
        'user' => [
            'class' => 'dektrium\user\Module',
            'enableUnconfirmedLogin' => true,
            'admins' => ['strose_admin', 'rvoliquino'],
            'enableFlashMessages' => false
        ],
        'gridview' =>  [
            'class' => '\kartik\grid\Module'
            // enter optional module parameters below - only if you need to
            // use your own export download action or custom translation
            // message source
            // 'downloadAction' => 'gridview/export/download',
            // 'i18n' => []
        ],
        'ckeditor' => [
            'class' => 'wadeshuler\ckeditor\Module',

            'preset' => 'full-all',
            'customCdn' => 'https://cdn.ckeditor.com/4.11.3/full-all/ckeditor.js',    // must point to ckeditor.js

            // how to add external plugins (must also list them in `widgetClientOptions` `extraPlugins` below)
            // 'widgetExternalPlugins' => [
            //     ['name' => 'GoogleWebFonts', 'path' => '/web/ckeditor-gwf-plugin/', 'file' => 'plugin.js'],
            // ],

            // These are basically passed to the `CKEDITOR.replace()`
            // 'widgetClientOptions' => [
            //     'extraPlugins' => '/plugins/ckeditor-gwf-plugin',
            // ],

        ],
        'sliderrevolution' => [
            'class' => 'wadeshuler\sliderrevolution\SliderModule',
            'pluginLocation' => '@app/views/revolution',    // <-- path to your rs-plugin directory
        ],
        'social' => [
            // the module class
            'class' => 'kartik\social\Module',

            // the global settings for the facebook plugins widget
            'facebook' => [
                'appId' => '249163229274244',
                'secret' => 'f68eb6da99a31b8848d884669749cd7b',
            ],
            // the global settings for the Google Analytics plugin widget
            'googleAnalytics' => [
                'id' => '461586791169-76bnv6fmgnf3p25k84ejerg2edr02j5o.apps.googleusercontent.com',
                'domain' => 'stroseoflimacrockettca.org',
            ],

        ],
        'datecontrol' =>  [
            'class' => '\kartik\datecontrol\Module'
        ],
        'noty' => [
            'class' => 'lo\modules\noty\Module',
        ],
        'pdfjs' => [
            'class' => '\yii2assets\pdfjs\Module',
        ],
    ],

    'params' => $params,
];

if (YII_ENV_DEV) {
    // configuration adjustments for 'dev' environment
    $config['bootstrap'][] = 'debug';
    $config['modules']['debug'] = [
        'class' => 'yii\debug\Module',
        // uncomment the following to add your IP if you are not connecting from localhost.
        //'allowedIPs' => ['127.0.0.1', '::1'],
    ];

    $config['bootstrap'][] = 'gii';
    $config['modules']['gii'] = [
        'class' => 'yii\gii\Module',
        // uncomment the following to add your IP if you are not connecting from localhost.
        //'allowedIPs' => ['127.0.0.1', '::1'],
    ];
}

return $config;
