<?php

namespace app\helpers;

use <PERSON><PERSON>;
use yii\web\UploadedFile;

/**
 * Secure File Upload Helper
 *
 * Provides centralized, secure file upload handling for the entire application.
 * All controllers should use this helper instead of implementing their own upload logic.
 */
class SecureUploadHelper
{
    // Default configuration
    const DEFAULT_MAX_SIZE = 5242880; // 5MB
    const DEFAULT_ALLOWED_EXTENSIONS = ['jpg', 'jpeg', 'png', 'gif'];
    const DEFAULT_ALLOWED_MIME_TYPES = ['image/jpeg', 'image/png', 'image/gif'];

    // Carousel specific configuration
    const CAROUSEL_MAX_SIZE = 5242880; // 5MB
    const CAROUSEL_ALLOWED_EXTENSIONS = ['jpg', 'jpeg', 'png', 'gif'];
    const CAROUSEL_ALLOWED_MIME_TYPES = ['image/jpeg', 'image/png', 'image/gif'];

    // Document configuration
    const DOCUMENT_MAX_SIZE = 10485760; // 10MB
    const DOCUMENT_ALLOWED_EXTENSIONS = ['pdf'];
    const DOCUMENT_ALLOWED_MIME_TYPES = ['application/pdf'];

    /**
     * Securely upload a file with comprehensive validation
     *
     * @param UploadedFile $file The uploaded file instance
     * @param string $uploadDir The directory to save to (relative to webroot)
     * @param array $options Additional options for validation
     * @return string The secure filename of the uploaded file
     * @throws \Exception on validation or upload failure
     */
    public static function uploadFile(UploadedFile $file, $uploadDir = 'uploaded_pics', $options = [])
    {
        // Merge options with defaults
        $maxSize = $options['maxSize'] ?? self::DEFAULT_MAX_SIZE;
        $allowedExtensions = $options['allowedExtensions'] ?? self::DEFAULT_ALLOWED_EXTENSIONS;
        $allowedMimeTypes = $options['allowedMimeTypes'] ?? self::DEFAULT_ALLOWED_MIME_TYPES;

        // 1. Validate file exists and no errors
        if (!$file || $file->error !== UPLOAD_ERR_OK) {
            throw new \Exception('File upload error');
        }

        // 2. Validate file size
        if ($file->size > $maxSize) {
            $maxSizeMB = round($maxSize / 1024 / 1024, 1);
            throw new \Exception("File too large. Maximum size is {$maxSizeMB}MB.");
        }

        // 3. Validate file extension (whitelist approach)
        $extension = strtolower($file->extension);
        if (!in_array($extension, $allowedExtensions)) {
            $allowedStr = implode(', ', array_map('strtoupper', $allowedExtensions));
            throw new \Exception("Invalid file type. Only {$allowedStr} files are allowed.");
        }

        // 4. Validate MIME type
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file->tempName);
        finfo_close($finfo);

        if (!in_array($mimeType, $allowedMimeTypes)) {
            throw new \Exception('Invalid file format detected.');
        }

        // 5. For images, validate image content using getimagesize
        if (in_array($extension, ['jpg', 'jpeg', 'png', 'gif'])) {
            $imageInfo = getimagesize($file->tempName);
            if ($imageInfo === false) {
                throw new \Exception('File is not a valid image.');
            }
        }

        // 6. Scan for embedded malicious code
        $content = file_get_contents($file->tempName);
        $maliciousPatterns = [
            '/<\?php/i',
            '/<\?=/i',
            '/eval\s*\(/i',
            '/base64_decode\s*\(/i',
            '/system\s*\(/i',
            '/exec\s*\(/i',
            '/shell_exec\s*\(/i',
            '/passthru\s*\(/i',
            '/file_get_contents\s*\(/i',
            '/file_put_contents\s*\(/i',
            '/fopen\s*\(/i',
            '/fwrite\s*\(/i',
        ];

        foreach ($maliciousPatterns as $pattern) {
            if (preg_match($pattern, $content)) {
                throw new \Exception('Malicious content detected in file.');
            }
        }

        // 7. Generate secure filename
        $secureFilename = self::generateSecureFilename($extension);

        // 8. Ensure upload directory exists and is secure
        $fullUploadDir = Yii::getAlias('@webroot') . '/' . trim($uploadDir, '/') . '/';
        if (!is_dir($fullUploadDir)) {
            if (!mkdir($fullUploadDir, 0755, true)) {
                throw new \Exception('Failed to create upload directory.');
            }
        }

        // 9. Move file securely
        $targetPath = $fullUploadDir . $secureFilename;
        if (!$file->saveAs($targetPath)) {
            throw new \Exception('Failed to save uploaded file.');
        }

        // 10. Set secure file permissions
        chmod($targetPath, 0644);

        // 11. Log successful upload
        Yii::info("File uploaded successfully: {$secureFilename} to {$uploadDir}", __CLASS__);

        return $secureFilename;
    }

    /**
     * Upload multiple files
     *
     * @param UploadedFile[] $files Array of uploaded file instances
     * @param string $uploadDir The directory to save to
     * @param array $options Additional options for validation
     * @return array Array of secure filenames
     * @throws \Exception on validation or upload failure
     */
    public static function uploadMultipleFiles($files, $uploadDir = 'uploaded_pics', $options = [])
    {
        $uploadedFiles = [];

        foreach ($files as $file) {
            try {
                $uploadedFiles[] = self::uploadFile($file, $uploadDir, $options);
            } catch (\Exception $e) {
                // Clean up any files that were already uploaded
                foreach ($uploadedFiles as $filename) {
                    self::deleteFile($filename, $uploadDir);
                }
                throw $e;
            }
        }

        return $uploadedFiles;
    }

    /**
     * Generate a secure random filename
     *
     * @param string $extension File extension
     * @return string Secure filename
     */
    public static function generateSecureFilename($extension)
    {
        return bin2hex(random_bytes(16)) . '.' . $extension;
    }

    /**
     * Securely delete a file
     *
     * @param string $filename The filename to delete
     * @param string $uploadDir The directory containing the file
     * @return bool Success status
     */
    public static function deleteFile($filename, $uploadDir = 'uploaded_pics')
    {
        $filePath = Yii::getAlias('@webroot') . '/' . trim($uploadDir, '/') . '/' . $filename;

        if (file_exists($filePath)) {
            return unlink($filePath);
        }

        return true; // File doesn't exist, consider it deleted
    }

    /**
     * Get file URL for display
     *
     * @param string $filename The filename
     * @param string $uploadDir The directory containing the file
     * @return string Full URL to the file
     */
    public static function getFileUrl($filename, $uploadDir = 'uploaded_pics')
    {
        return Yii::$app->urlManager->createAbsoluteUrl('/' . trim($uploadDir, '/') . '/' . $filename);
    }

    /**
     * Validate uploaded file without saving it
     *
     * @param UploadedFile $file The uploaded file instance
     * @param array $options Additional options for validation
     * @return bool True if valid
     * @throws \Exception on validation failure
     */
    public static function validateFile(UploadedFile $file, $options = [])
    {
        // Use the same validation as uploadFile but don't save
        $maxSize = $options['maxSize'] ?? self::DEFAULT_MAX_SIZE;
        $allowedExtensions = $options['allowedExtensions'] ?? self::DEFAULT_ALLOWED_EXTENSIONS;
        $allowedMimeTypes = $options['allowedMimeTypes'] ?? self::DEFAULT_ALLOWED_MIME_TYPES;

        // Run all the same validations as uploadFile but stop before saving
        if (!$file || $file->error !== UPLOAD_ERR_OK) {
            throw new \Exception('File upload error');
        }

        if ($file->size > $maxSize) {
            $maxSizeMB = round($maxSize / 1024 / 1024, 1);
            throw new \Exception("File too large. Maximum size is {$maxSizeMB}MB.");
        }

        $extension = strtolower($file->extension);
        if (!in_array($extension, $allowedExtensions)) {
            $allowedStr = implode(', ', array_map('strtoupper', $allowedExtensions));
            throw new \Exception("Invalid file type. Only {$allowedStr} files are allowed.");
        }

        return true;
    }

    /**
     * Upload carousel image with specific validation
     *
     * @param UploadedFile $file The uploaded file instance
     * @return string The secure filename of the uploaded file
     * @throws \Exception on validation or upload failure
     */
    public static function uploadCarouselImage(UploadedFile $file)
    {
        return self::uploadFile($file, 'uploads/carousel', [
            'maxSize' => self::CAROUSEL_MAX_SIZE,
            'allowedExtensions' => self::CAROUSEL_ALLOWED_EXTENSIONS,
            'allowedMimeTypes' => self::CAROUSEL_ALLOWED_MIME_TYPES,
        ]);
    }

    /**
     * Validate carousel image without uploading
     *
     * @param UploadedFile $file The uploaded file instance
     * @return bool True if valid
     * @throws \Exception on validation failure
     */
    public static function validateCarouselImage(UploadedFile $file)
    {
        return self::validateFile($file, [
            'maxSize' => self::CAROUSEL_MAX_SIZE,
            'allowedExtensions' => self::CAROUSEL_ALLOWED_EXTENSIONS,
            'allowedMimeTypes' => self::CAROUSEL_ALLOWED_MIME_TYPES,
        ]);
    }

    /**
     * Delete file by relative path
     *
     * @param string $relativePath Path relative to webroot
     * @return bool Success status
     */
    public static function deleteFileByPath($relativePath)
    {
        if (!$relativePath) {
            return true;
        }

        $fullPath = Yii::getAlias('@webroot/' . ltrim($relativePath, '/'));
        if (file_exists($fullPath)) {
            return unlink($fullPath);
        }
        return true;
    }
}
