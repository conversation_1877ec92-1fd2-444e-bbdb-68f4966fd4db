<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model app\models\Carousel */
/* @var $form yii\widgets\ActiveForm */
?>

<div class="carousel-form">

    <?php $form = ActiveForm::begin([
        'options' => ['enctype' => 'multipart/form-data'],
        'fieldConfig' => [
            'template' => "{label}\n{input}\n{hint}\n{error}",
            'labelOptions' => ['class' => 'control-label'],
        ],
    ]); ?>

    <div class="row">
        <div class="col-md-8">
            
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <i class="fa fa-edit"></i> Slide Content
                    </h4>
                </div>
                <div class="panel-body">
                    
                    <?= $form->field($model, 'title')->textInput([
                        'maxlength' => true,
                        'placeholder' => 'Enter slide title...',
                        'class' => 'form-control',
                    ])->hint('The main title text that will appear on the slide') ?>

                    <?= $form->field($model, 'subtitle')->textarea([
                        'rows' => 3,
                        'placeholder' => 'Enter slide subtitle or description...',
                        'class' => 'form-control',
                    ])->hint('Optional subtitle or description text') ?>

                    <div class="row">
                        <div class="col-md-6">
                            <?= $form->field($model, 'display_order')->textInput([
                                'type' => 'number',
                                'min' => 1,
                                'class' => 'form-control',
                            ])->hint('Lower numbers appear first (1, 2, 3...)') ?>
                        </div>
                        <div class="col-md-6">
                            <?= $form->field($model, 'is_active')->checkbox([
                                'class' => 'form-control',
                                'style' => 'margin-top: 10px;'
                            ])->hint('Uncheck to hide this slide from the carousel') ?>
                        </div>
                    </div>

                </div>
            </div>

        </div>
        
        <div class="col-md-4">
            
            <div class="panel panel-info">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <i class="fa fa-image"></i> Background Image
                    </h4>
                </div>
                <div class="panel-body">
                    
                    <?= $form->field($model, 'imageFile')->fileInput([
                        'accept' => 'image/*',
                        'class' => 'form-control',
                    ])->hint('Upload JPG, PNG, or GIF background image (max 5MB)') ?>

                    <?= $form->field($model, 'foregroundImageFile')->fileInput([
                        'accept' => 'image/*',
                        'class' => 'form-control',
                    ])->hint('Upload JPG, PNG, or GIF foreground image like people, objects (max 5MB, optional)') ?>

                    <?= $form->field($model, 'background_fit')->dropDownList([
                        'cover' => 'Cover (fills entire area, may crop)',
                        'contain' => 'Contain (fits entirely, may have empty space)',
                        'fill' => 'Fill (stretches to fill, may distort)',
                        'scale-down' => 'Scale Down (like contain but never larger than original)'
                    ], [
                        'class' => 'form-control',
                        'prompt' => 'Select background fit...'
                    ])->hint('How the background image should fit in the carousel area') ?>

                    <?php if ($model->background_image_path): ?>
                        <div class="current-image" style="margin-top: 15px;">
                            <label class="control-label">Current Background Image:</label>
                            <div class="text-center">
                                <?php $imageUrl = Yii::$app->request->baseUrl . '/' . $model->background_image_path; ?>
                                <?= Html::img($imageUrl, [
                                    'class' => 'img-responsive img-thumbnail',
                                    'style' => 'max-width: 100%; max-height: 200px;'
                                ]) ?>
                                <p class="text-muted" style="margin-top: 5px;">
                                    <small><?= Html::encode(basename($model->background_image_path)) ?></small>
                                </p>
                            </div>
                        </div>
                    <?php endif; ?>

                    <?php if ($model->foreground_image_path): ?>
                        <div class="current-image" style="margin-top: 15px;">
                            <label class="control-label">Current Foreground Image:</label>
                            <div class="text-center">
                                <?php $foregroundUrl = Yii::$app->request->baseUrl . '/' . $model->foreground_image_path; ?>
                                <?= Html::img($foregroundUrl, [
                                    'class' => 'img-responsive img-thumbnail',
                                    'style' => 'max-width: 100%; max-height: 200px;'
                                ]) ?>
                                <p class="text-muted" style="margin-top: 5px;">
                                    <small><?= Html::encode(basename($model->foreground_image_path)) ?></small>
                                </p>
                            </div>
                        </div>
                    <?php endif; ?>

                    <div class="alert alert-info" style="margin-top: 15px;">
                        <small>
                            <i class="fa fa-info-circle"></i>
                            <strong>Image Guidelines:</strong><br>
                            • <strong>Background:</strong> Recommended size 1920x1080px<br>
                            • <strong>Foreground:</strong> PNG with transparency works best<br>
                            • Supported formats: JPG, PNG, GIF<br>
                            • Maximum file size: 5MB each<br>
                            • Background displays behind text, foreground displays in front
                        </small>
                    </div>

                </div>
            </div>

            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <i class="fa fa-question-circle"></i> Help
                    </h4>
                </div>
                <div class="panel-body">
                    <small>
                        <strong>Display Order:</strong> Controls the sequence of slides. Lower numbers appear first.<br><br>
                        <strong>Active Status:</strong> Only active slides will be shown in the carousel.<br><br>
                        <strong>Line Breaks:</strong> Use &lt;br&gt; in title/subtitle for line breaks.<br><br>
                        <strong>Image Upload:</strong> Leave empty to keep current images when updating.
                    </small>
                </div>
            </div>

        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="form-group">
                <div class="btn-group">
                    <?= Html::submitButton($model->isNewRecord ? '<i class="fa fa-save"></i> Create Slide' : '<i class="fa fa-save"></i> Update Slide', [
                        'class' => $model->isNewRecord ? 'btn btn-success' : 'btn btn-primary'
                    ]) ?>
                    <?= Html::a('<i class="fa fa-times"></i> Cancel', ['index'], [
                        'class' => 'btn btn-default'
                    ]) ?>
                </div>
            </div>
        </div>
    </div>

    <?php ActiveForm::end(); ?>

</div>

<?php
// Add some custom CSS for better form styling
$this->registerCss("
.carousel-form .panel {
    margin-bottom: 20px;
}
.carousel-form .form-group {
    margin-bottom: 15px;
}
.carousel-form .hint-block {
    color: #737373;
    font-size: 12px;
    margin-top: 5px;
}
.carousel-form .current-image {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
    background-color: #f9f9f9;
}
.carousel-form .alert-info {
    font-size: 12px;
}
");

// Add JavaScript for form enhancements
$this->registerJs("
// Preview uploaded image
$('#carousel-imagefile').change(function() {
    var input = this;
    if (input.files && input.files[0]) {
        var reader = new FileReader();
        reader.onload = function(e) {
            // Create or update preview
            var preview = $('#image-preview');
            if (preview.length === 0) {
                $('#carousel-imagefile').after('<div id=\"image-preview\" style=\"margin-top: 10px; text-align: center;\"><img style=\"max-width: 100%; max-height: 200px; border-radius: 4px;\" /></div>');
                preview = $('#image-preview');
            }
            preview.find('img').attr('src', e.target.result);
        }
        reader.readAsDataURL(input.files[0]);
    }
});
");
?>
