# Dynamic Carousel Management System

## Overview
A comprehensive admin-manageable carousel system for the St. Rose of Lima church website, built with Yii2 framework. This system allows non-technical administrators to manage homepage carousel slides without requiring developer intervention.

## Features Implemented

### 🗄️ Database & Backend
- **Database Table**: `carousel` table with fields for ID, title, subtitle, background image path, display order, active status, and timestamps
- **Yii2 Model**: `Carousel` ActiveRecord model with validation rules, relationships, and helper methods
- **CRUD Operations**: Full Create, Read, Update, Delete functionality
- **File Upload**: Secure image upload with validation (JPG, JPEG, PNG, GIF, max 5MB)
- **Display Ordering**: Drag-and-drop style reordering with up/down arrows
- **Status Management**: Enable/disable slides without deletion

### 🔐 Admin Interface
- **Admin-Only Access**: Restricted to users with admin privileges (`strose_admin`, `rvoliquino`)
- **Navigation Integration**: Added "Carousel Management" link to the existing "Maintenance" section
- **Responsive Design**: Mobile-friendly admin interface
- **User-Friendly Forms**: Intuitive forms with image preview and validation feedback
- **Bulk Operations**: Move slides up/down, toggle active status
- **Visual Feedback**: Success/error flash messages for all operations

### 🎨 Frontend Integration
- **Dynamic Loading**: Carousel slides loaded from database
- **Fallback Content**: Maintains existing hardcoded slides when no database slides exist
- **Revolution Slider Compatible**: Preserves all existing carousel functionality
- **Ken Burns Effects**: Maintains existing visual effects and transitions
- **Responsive Display**: Works across all device sizes

### 🛡️ Security Features
- **Secure File Upload**: Comprehensive validation and sanitization
- **Access Control**: Admin-only access with proper authentication checks
- **File Type Validation**: Whitelist approach for allowed file types
- **Size Limits**: Enforced file size limits (5MB for images)
- **Path Security**: Secure file storage with proper naming conventions

## File Structure

```
├── controllers/
│   └── CarouselController.php          # Admin CRUD controller
├── models/
│   └── Carousel.php                    # ActiveRecord model
├── views/
│   └── carousel/
│       ├── index.php                   # List all slides
│       ├── create.php                  # Create new slide
│       ├── update.php                  # Edit existing slide
│       ├── view.php                    # View slide details
│       └── _form.php                   # Form component
├── views/layouts/
│   └── main.php                        # Updated navigation
├── views/site/
│   └── index.php                       # Updated frontend carousel
├── migrations/
│   └── m250121_000000_create_carousel_table.php
├── helpers/
│   └── SecureUploadHelper.php          # Enhanced upload helper
└── test-carousel-system.php            # System test script
```

## Installation & Setup

### 1. Run Database Migration
```bash
php yii migrate
```

### 2. Set Directory Permissions
```bash
mkdir -p uploads/carousel
chmod 755 uploads/carousel
```

### 3. Test System
```bash
php test-carousel-system.php
```

### 4. Access Admin Interface
Navigate to: `https://yoursite.com/carousel`
(Requires admin login)

## Usage Guide

### For Administrators

#### Creating a New Slide
1. Go to **Maintenance > Carousel Management**
2. Click **"Create New Slide"**
3. Fill in the form:
   - **Title**: Main heading text
   - **Subtitle**: Optional description text
   - **Background Image**: Upload JPG/PNG/GIF (max 5MB)
   - **Display Order**: Number for slide sequence (1, 2, 3...)
   - **Active**: Check to make slide visible
4. Click **"Create Slide"**

#### Managing Existing Slides
- **Edit**: Click the edit icon to modify slide content
- **Move Up/Down**: Use arrow buttons to reorder slides
- **Activate/Deactivate**: Toggle visibility without deletion
- **Delete**: Remove slide permanently (with confirmation)

#### Image Guidelines
- **Recommended Size**: 1920x1080px (Full HD)
- **Supported Formats**: JPG, JPEG, PNG, GIF
- **Maximum File Size**: 5MB
- **Aspect Ratio**: 16:9 works best for full-screen display

### For Developers

#### Model Usage
```php
// Get active slides
$slides = Carousel::getActiveItems()->all();

// Create new slide
$slide = new Carousel();
$slide->title = 'Welcome';
$slide->subtitle = 'Join our community';
$slide->display_order = 1;
$slide->is_active = true;
$slide->save();

// Upload image
$slide->imageFile = UploadedFile::getInstance($slide, 'imageFile');
$slide->uploadImage();
```

#### Controller Integration
```php
// In any controller action
$carouselSlides = Carousel::getActiveItems()->all();
return $this->render('view', ['carouselSlides' => $carouselSlides]);
```

## Technical Specifications

### Database Schema
```sql
CREATE TABLE `carousel` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `subtitle` text,
  `background_image_path` varchar(500),
  `display_order` int(11) NOT NULL DEFAULT 0,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx-carousel-is_active` (`is_active`),
  KEY `idx-carousel-display_order` (`display_order`),
  KEY `idx-carousel-active_order` (`is_active`,`display_order`)
);
```

### Security Measures
- Admin-only access control
- File type validation (whitelist approach)
- File size limits enforcement
- Secure filename generation
- Path traversal protection
- CSRF protection on all forms

### Performance Optimizations
- Database indexes on frequently queried columns
- Lazy loading of images in Revolution Slider
- Optimized queries with proper ordering
- Efficient file storage structure

## Troubleshooting

### Common Issues

#### 1. "Access Denied" Error
- Ensure user has admin privileges
- Check if username is in admin list: `['strose_admin', 'rvoliquino']`

#### 2. Image Upload Fails
- Check file size (max 5MB)
- Verify file format (JPG, PNG, GIF only)
- Ensure upload directory exists and is writable

#### 3. Slides Not Appearing
- Verify slides are marked as "Active"
- Check display order values
- Clear browser cache

#### 4. Database Errors
- Run migration: `php yii migrate`
- Check database connection in `config/db.php`

### Support
For technical support or feature requests, contact the development team.

## Future Enhancements
- Drag-and-drop reordering interface
- Bulk upload functionality
- Image cropping/resizing tools
- Slide scheduling (start/end dates)
- Analytics integration
- Multi-language support
