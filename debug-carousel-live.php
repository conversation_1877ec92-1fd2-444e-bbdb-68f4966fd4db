<?php
/**
 * Simple Carousel Debugging Script
 * Check database and files without full Yii2 bootstrap
 */

echo "=== SIMPLE CAROUSEL DEBUG ===\n";
echo "Timestamp: " . date('Y-m-d H:i:s') . "\n\n";

// Database connection details (update these with your actual credentials)
$host = 'localhost';
$dbname = 'stroseo1_strose_db';
$username = 'stroseo1_rvoliqu';
$password = 'somethingcool';

try {
    echo "1. CONNECTING TO DATABASE...\n";
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "   ✓ Database connected successfully\n\n";

echo "=== LIVE CAROUSEL DEBUG ===\n";
echo "Timestamp: " . date('Y-m-d H:i:s') . "\n\n";

try {
    // 1. Check database connection
    echo "1. DATABASE CONNECTION:\n";
    $db = Yii::$app->db;
    $db->open();
    echo "   ✓ Database connected successfully\n";
    echo "   Database: " . $db->dsn . "\n\n";

    // 2. Check if carousel table exists
    echo "2. CAROUSEL TABLE:\n";
    $tableSchema = $db->getTableSchema('carousel');
    if ($tableSchema) {
        echo "   ✓ Carousel table exists\n";
        echo "   Columns: " . implode(', ', array_keys($tableSchema->columns)) . "\n";
    } else {
        echo "   ✗ Carousel table does not exist\n";
        echo "   Run: php yii migrate\n";
        exit(1);
    }

    // 3. Check carousel records
    echo "\n3. CAROUSEL RECORDS:\n";
    $allSlides = app\models\Carousel::find()->all();
    echo "   Total records: " . count($allSlides) . "\n";
    
    if (count($allSlides) === 0) {
        echo "   ⚠ No carousel records found!\n";
        echo "   Creating sample records...\n";
        
        // Create sample records
        $slide1 = new app\models\Carousel();
        $slide1->title = 'Welcome to St. Rose of Lima';
        $slide1->subtitle = 'Come and Join Us in Crockett, CA';
        $slide1->background_image_path = 'images/st_rose_header1/galaxy_2.jpg';
        $slide1->foreground_image_path = 'images/st_rose_header1/priest2.png';
        $slide1->display_order = 1;
        $slide1->is_active = true;
        $slide1->save();
        
        $slide2 = new app\models\Carousel();
        $slide2->title = 'Be beacons of hope. Reflect God\'s love.<br />Spread acceptance and understanding';
        $slide2->subtitle = '- Pope Leo XIV';
        $slide2->background_image_path = 'images/st_rose_header1/red_curtain.jpg';
        $slide2->foreground_image_path = 'images/st_rose_header1/popeleo.png';
        $slide2->display_order = 2;
        $slide2->is_active = true;
        $slide2->save();
        
        echo "   ✓ Created 2 sample records\n";
        $allSlides = app\models\Carousel::find()->all();
    }

    foreach ($allSlides as $slide) {
        echo "   ID: {$slide->id}\n";
        echo "   Title: {$slide->title}\n";
        echo "   Background: {$slide->background_image_path}\n";
        echo "   Foreground: " . ($slide->foreground_image_path ?? 'NULL') . "\n";
        echo "   Active: " . ($slide->is_active ? 'YES' : 'NO') . "\n";
        echo "   ---\n";
    }

    // 4. Check active slides (what the controller uses)
    echo "\n4. ACTIVE SLIDES (Controller Query):\n";
    $activeSlides = app\models\Carousel::getActiveItems()->all();
    echo "   Active slides count: " . count($activeSlides) . "\n";
    
    foreach ($activeSlides as $slide) {
        echo "   [{$slide->id}] {$slide->title} (Order: {$slide->display_order})\n";
    }

    // 5. Check image files
    echo "\n5. IMAGE FILE CHECKS:\n";
    foreach ($activeSlides as $slide) {
        if ($slide->background_image_path) {
            $bgPath = __DIR__ . '/' . $slide->background_image_path;
            $exists = file_exists($bgPath);
            echo "   Background [{$slide->id}]: " . ($exists ? '✓' : '✗') . " {$slide->background_image_path}\n";
            if ($exists) {
                echo "     Size: " . number_format(filesize($bgPath)) . " bytes\n";
            }
        }
        
        if ($slide->foreground_image_path) {
            $fgPath = __DIR__ . '/' . $slide->foreground_image_path;
            $exists = file_exists($fgPath);
            echo "   Foreground [{$slide->id}]: " . ($exists ? '✓' : '✗') . " {$slide->foreground_image_path}\n";
            if ($exists) {
                echo "     Size: " . number_format(filesize($fgPath)) . " bytes\n";
            }
        }
    }

    // 6. Test controller data
    echo "\n6. CONTROLLER DATA SIMULATION:\n";
    $carouselSlides = app\models\Carousel::getActiveItems()->all();
    echo "   Variable type: " . gettype($carouselSlides) . "\n";
    echo "   Count: " . count($carouselSlides) . "\n";
    echo "   Empty check: " . (empty($carouselSlides) ? 'EMPTY' : 'NOT EMPTY') . "\n";

    // 7. Revolution Slider files check
    echo "\n7. REVOLUTION SLIDER FILES:\n";
    $revFiles = [
        'views/revolution/js/jquery.themepunch.tools.min.js',
        'views/revolution/js/jquery.themepunch.revolution.min.js',
        'views/revolution/css/settings.css',
        'views/revolution/css/layers.css',
        'views/revolution/css/navigation.css'
    ];
    
    foreach ($revFiles as $file) {
        $path = __DIR__ . '/' . $file;
        $exists = file_exists($path);
        echo "   " . ($exists ? '✓' : '✗') . " {$file}\n";
        if ($exists) {
            echo "     Size: " . number_format(filesize($path)) . " bytes\n";
        }
    }

} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

    echo "\n=== DEBUG COMPLETE ===\n";
    echo "Next steps:\n";
    echo "1. Check browser console at: https://www.strosecrockett.org/\n";
    echo "2. Look for Revolution Slider debug messages\n";
    echo "3. Check Network tab for failed resource requests\n";
    echo "4. Verify carousel HTML is being generated\n";

} catch (PDOException $e) {
    echo "Database Error: " . $e->getMessage() . "\n";
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
