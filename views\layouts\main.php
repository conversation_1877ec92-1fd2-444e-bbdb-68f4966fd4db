<?php

/* @var $this \yii\web\View */
/* @var $content string */

use app\widgets\Alert;
use yii\helpers\Html;
use yii\helpers\Url;
use yii\bootstrap\Nav;
use yii\bootstrap\NavBar;
use yii\widgets\Breadcrumbs;
use app\assets\AppAsset;
use app\models\ChurchInfo;
use app\models\ChurchAddress;
use app\models\DailySched;
use app\models\ContactUs;
use app\models\SocialLinks;
use app\assets\FontAsset;

use kartik\dialog\Dialog;
use lo\modules\noty\Wrapper;

AppAsset::register($this);
FontAsset::register($this);

rmrevin\yii\fontawesome\AssetBundle::register($this);



// widget with default options
// echo Dialog::widget([
//     'libName' => 'krajeeDialogCust1',
//     'options' => [  // customized BootstrapDialog options
//         'size' => Dialog::SIZE_WIDE, // large dialog text
//         'type' => Dialog::TYPE_INFO, // bootstrap contextual color
//         'title' => 'Send Email',
//         'buttons' => [
//             [
//                 'id' => 'cust-cancel-btn',
//                 'label' => 'Cancel',
//                 'cssClass' => 'btn-default',
//                 'hotkey' => 'C',
                
//             ],
//             [
//                 'id' => 'cust-submit-btn',
//                 'label' => 'Submit',
//                 'cssClass' => 'btn-primary',
//                 'hotkey' => 'S',
                
//             ]
//         ]
//     ]

// ]);
$sql = "select social_address_url from social_links where social_name = 'Facebook'";
$social_link = Yii::$app->db->createCommand($sql)->queryScalar();

// var_dump( $social_link); die();

$about = ['label' => 'About',
          'url' => '#',
          'items' => [] ];
$sacraments = ['label' => 'Sacraments',
          'url' => '#',
          'items' => [] ];

$aboutChurchInfo = ChurchInfo::find()->where(['category' => 1])->orderBy(['sorted_by' => SORT_ASC])->all();
foreach($aboutChurchInfo as $info){
    $about['items'][]=[
        'label' =>  ucwords(strtolower($info->title)),
        'url' => Url::to(['site/about','id' => $info->id ]) 
    ];
}

// add pastoral council
$about['items'][]=[
    'label' =>  'Parish Council',
    'url' => Url::to(['/parish-council/view-all']) 
];


$sacramentsChurchInfo = ChurchInfo::find()->where(['category' => 2])->orderBy(['sorted_by' => SORT_ASC])->all();
foreach($sacramentsChurchInfo as $info){
    $sacraments['items'][]=[
        'label' => $info->title,
        'url' => Url::to(['site/about','id' => $info->id ]) 
    ];
}

// get church address
$churchAddr = ChurchAddress::findOne(1);

// get daily schedule
$dailySched = DailySched::find()->orderBy(['sort_key' => SORT_ASC])->all();

// get contact information
$contact = ContactUs::find()->orderBy(['sort_key' => SORT_ASC])->all();

// get social links
$socialLinks = SocialLinks::find()->all();

// echo "<pre>";
// var_dump( $churchAddr);
// die();


    echo newerton\fancybox3\FancyBox::widget([
        'target' => '[fancybox-churchmap]'
    ]);
    

?>
<?php $this->beginPage() ?>
<!DOCTYPE html>
<html lang="<?= Yii::$app->language ?>">
<head>

    <!-- Global site tag (gtag.js) - Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=UA-128988543-1"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());

        gtag('config', 'UA-128988543-1');
    </script>

    <meta charset="<?= Yii::$app->charset ?>">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="icon" type="image/png" sizes="96x96" href="<?= Yii::$app->request->baseUrl ?>/images/favicon-96x96.png">
    
    <?= Html::csrfMetaTags() ?>
    <title><?= Html::encode($this->title) ?></title>
    <?php $this->head() ?>
</head>
<body>
<?php $this->beginBody() ?>

<div class="wrap">
    <?php
    NavBar::begin([
        'brandLabel' => Yii::$app->name,
        'brandUrl' => Yii::$app->homeUrl,
        'options' => [
            'class' => 'navbar-inverse navbar-fixed-top',
        ],
    ]);
    echo Nav::widget([
        'options' => ['class' => 'navbar-nav navbar-right'],
        'items' => [
            ['label' => 'Home', 'url' => ['/site/index']],
            // [
            //     'label' => 'About',
            //     'icon' => 'share',
            //     'url' => '#',
            //     'items' => [
            //         [
            //             //( $about )
            //             'label' => 'The Beginning of the Hillside Parish', 'icon' => 'file-code-o', 'url' => ['/site/about'],
                    
            //         ],
            //     ]
            // ],
            ( $about ),
            ( $sacraments ),
            [
                'label' => 'Events', 
                'url' => ['#'],
                'items' => [
                    ['label' => 'Upcoming Events', 'url' => ['/event/upcoming']],
                    ['label' => 'Past Events', 'url' => ['/event/past']],
                ]
            ],
            //['label' => 'Upcoming Events', 'url' => ['/event/upcoming']],
            // ['label' => 'Newsletter', 'url' => ['/newsletter/view-all']],
            ['label' => 'Bulletin', 'url' => ['/bulletin/view-all']],
            ['label' => 'Voices', 'url' => ['/homily/view-all']],
            //['label' => 'Past Events', 'url' => ['/event/past']],

            // [
            //     'label' => 'Gallery', 
            //     'url' => ['#'],
            //     'items' => [
            //         [ 'label' => 'Photo', 'url' => ['/media-photo/display-album']],
            //         [ 'label' => 'Video', 'url' => ['/user/admin/index']],
            //         [ 'label' => 'Test', 'url' => ['/media-photo/photo']],
            //     ],
            // ],

            // ['label' => 'About', 'url' => ['/site/about']],
            //['label' => 'Contact', 'url' => ['/site/contact']],
            
            !Yii::$app->user->isGuest ? (
                [
                    'label' => 'Maintenance', 
                    'url' => ['#'],
                    'items' => [
                        [ 'label' => 'Carousel Management', 'url' => ['/carousel']],
                        [ 'label' => 'Church Information', 'url' => ['/church-info']],
                        [ 'label' => 'Parish Council', 'url' => ['/parish-council']],
                        [ 'label' => 'Events', 'url' => ['/event']],
                        [ 'label' => 'Current Priest', 'url' => ['/current-priest']],
                        //[ 'label' => 'Newsletter', 'url' => ['/newsletter']],
                        [ 'label' => 'Bulletin', 'url' => ['/bulletin']],
                        [ 'label' => 'Voices', 'url' => ['/homily']],
                        [ 'label' => 'Office Address', 'url' => ['/church-address']],
                        [ 'label' => 'Mass Schedules', 'url' => ['/daily-sched']],
                        [ 'label' => 'Contact Us', 'url' => ['/contact-us']],
                        [ 'label' => 'Social Links', 'url' => ['/social-links']],
                        [ 'label' => 'Bulletin Board', 'url' => ['/bulletin-board']],
                        [ 'label' => 'News Section-1', 'url' => ['/news-section1']],
                        [ 'label' => 'News Section-2', 'url' => ['/news-section2']],
                        
                        // [ 'label' => 'Photo', 'url' => ['/media-photo/photo']],
                        // [ 'label' => 'Category of Information', 'url' => ['/category']],
                        
                        [ 'label' => 'User Management', 'url' => ['/user/admin/index']],
                        // [ 'label' => 'Photo Album', 'url' => ['/event/list-photo-album']],
                        //[ 'label' => 'Album ID', 'url' => ['/media-photo/album']],
                        // [ 'label' => 'Photo Album', 'url' => ['/media-photo/album']],
                        // [ 'label' => 'Save Photo Url', 'url' => ['/media-photo/traverse']],
                        [ 'label' => 'Album Management', 'url' => ['/album']],
                        // [ 'label' => 'Upload Image', 'url' => ['/uploaded-pics/upload']],
                        // [ 'label' => 'Google Photos', 'url' => ['/gallery-photo']],

                    ],
                ]
            ) : (''),

            // Yii::$app->user->isGuest ? (
            //     ['label' => 'Login', 'url' => ['/user/security/login']]
            // ) : (
            //     '<li>'
            //     . Html::beginForm(['/user/security/logout'], 'post')
            //     . Html::submitButton(
            //         'Logout (' . Yii::$app->user->identity->username . ')',
            //         ['class' => 'btn btn-link logout']
            //     )
            //     . Html::endForm()
            //     . '</li>'
            // ),

            

        ],
    ]);
    NavBar::end();
    ?>


    <div class="container-fluid">
       
        <?= Breadcrumbs::widget([
            'links' => isset($this->params['breadcrumbs']) ? $this->params['breadcrumbs'] : [],
        ]) ?>
        <?= Alert::widget() ?>
        <!-- for Bootstrap Alert -->
        <?php //= Wrapper::widget(); ?>

        <?php if (Yii::$app->session->hasFlash('success')): ?>
            <div class="alert alert-success alert-dismissable">
                <button aria-hidden="true" data-dismiss="alert" class="close" type="button">×</button>
                <h4><i class="icon fa fa-check"></i>Saved!</h4>
                <?= Yii::$app->session->getFlash('success') ?>
                
            </div>
        <?php endif; ?>

        <?php if (Yii::$app->session->hasFlash('error')): ?>
            <div id='error-message' class="alert alert-danger alert-dismissable">
                <button aria-hidden="true" data-dismiss="alert" class="close" type="button">×</button>
                <h4><i class="icon fa fa-check"></i>Saved!</h4>
                <?= Yii::$app->session->getFlash('error') ?>
            </div>
        <?php endif; ?>

        <?= $content ?>
        
    </div>
</div>

<footer class="footer">
    <div class="container">
        <div class="row">
            <div class="col-lg-3">
                    <h4 class="footer_header">Office Address</h4>
                    <?php
                        echo $churchAddr['address1']."<br />".
                             $churchAddr['address2']."<br />".
                             $churchAddr['address3']."<br />";
                    ?>
                    <br />
                    <p><?= Html::a('Location Map','https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d1570.8473297789913!2d-122.22349042803451!3d38.054200462883465!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x80857178fd9cdcc5%3A0x8d30855638a17b42!2sSt.+Rose+of+Lima+Church!5e0!3m2!1sen!2sph!4v1537017578145',
                                    ['class'=>'btn btn-success fancybox-churchmap']); ?></p>
            </div>
            <div class="col-lg-6">
                <h4 class="footer_header">Mass Schedules</h4>
                <table width="100%">
                    <?php
                        foreach($dailySched as $sched){
                            echo "<tr><td style='padding-right: 5px;'>".$sched['activity']."</td><td>".$sched['time_schedule']."</td></tr>";
                        }

                    ?>
                </table>    
                
            </div>
            <div class="col-lg-3">
                <div class="row">
                    <div class="col-lg-12">
                        <h4 class="footer_header">Contact Us</h4>
                        <table width="100%">
                        <?php
                            $index=0;
                            foreach($contact as $temp){
                                $index++;
                                if($index == 3){
                                    echo "<tr><td class='footer_header'>".$temp['contact_info']."</td><td ><a href='".Url::to(['/church-email/create'])."' title='Click to Send Email' class='church-email'>".$temp['contact_details']."</a></td></tr>";
                                }else{
                                    echo "<tr><td class='footer_header'>".$temp['contact_info']."</td><td>".$temp['contact_details']."</td></tr>";
                                }
                                
                            }
                        ?>
                        </table>
                        <p style='margin-top: 5px;'>Please contact our Parish Secretary for more information..</p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-12">
                        <?= Html::a('Follow Us on Facebook',$social_link,['class'=>'social-media', 'target'=>'_blank']); ?>
                        <!-- <h4 class="footer_header">Follow Us on Facebook</h4> -->
                        <?php
                            // foreach($socialLinks as $link){
                            //     echo "<span><a href='".$link['social_address_url']."' title='".$link['social_name']."' class='social_acct' target='_blank'>".
                            //     $link['social_site']."</a> </span>";
                            // }
                        ?>
                    </div>
                </div>
                <div class="row " style='padding-top: 10px; '>
                    <div class="col-lg-12">
                    <?php echo
                        Yii::$app->user->isGuest ? 
                        Html::a('Login', ['/user/security/login'], ['class' => 'login-link']) :
                        // Html::a('Logout ('. Yii::$app->user->identity->username ." )", ['/user/security/login'], ['class' => 'login-link'])
                    //     (
                    //     ['label' => 'Login', 'url' => ['/user/security/login']]
                    // ) 
                    // : (
                        ( "<div class='pull-left'>".
                         Html::beginForm(['/user/security/logout'], 'post')
                        . Html::submitButton(
                            'Logout (' . Yii::$app->user->identity->username . ')',
                            ['class' => 'btn btn-primary']
                        )
                        . Html::endForm()."</div>"
                        );
                    // )
                    ?>
                    </div>
                </div>
            </div>
            <!-- <div class="col-lg-2">
                
            </div> -->
        </div>
        

        <!--<p class="pull-right"><?php //= Yii::powered() ?></p>-->
    </div>
</footer>
<div class='container-fluid' style="background-color: #d14d42; margin-top: 0px;">
    <div class="container">
        <div class="row">
        <div class="col-lg-3 copyright">
            <p class="pull-left"><h5>&copy; St. Rose of Lima, Crockett <?= date('Y') ?></h5></p>
        </div>
        </div>
    </div>
</div>

<?php
$script = <<< JS
$(document).ready(function () {
    $('.fancybox-churchmap').fancybox({
        // 'autoScale'    : false, // not valid in v2.x, use autoSize instead
        // 'transitionIn' : 'none', // not valid in v2.x, use openEffect instead
        // 'transitionOut': 'none', // not valid in v2.x, use closeEffect instead
        //width      : '75%',
        //height     : '75%',
        autoSize   : false,
        openEffect : 'none',
        closeEffect: 'none',
        type       : "iframe",
        iframe     : {
           preload : false // this will prevent to place map off center
        }
    });

    // $(".church-email").on("click", function() {
    //     krajeeDialogCust1.dialog(
    //         'This is a <b>custom dialog</b>. The dialog box is <em>draggable</em> by default and <em>closable</em> ' +
    //         '(try it). Note that the Ok and Cancel buttons will do nothing here until you write the relevant JS code ' +
    //         'for the buttons within "options". Exit the dialog by clicking the cross icon on the top right.',
    //         function (result) {

    //         }
    //     );
    // });


});

JS;

$this->registerJs($script);

?>


<?php $this->endBody() ?>



</body>
</html>
<?php $this->endPage() ?>

